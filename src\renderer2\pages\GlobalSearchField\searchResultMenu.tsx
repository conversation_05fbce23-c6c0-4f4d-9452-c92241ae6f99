import React, { useState, useRef, useEffect } from 'react';
import { Popper, Paper, ClickAwayListener } from '@mui/material';
import styles from "./searchBox.module.scss";

type MenuItem = {
  id: string;
  label: string;
  date: string;
};

const SearchResultMenu = ({
  anchorEl,
  open,
  onClose,
  items,
  onItemClick,
}: {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  items: MenuItem[];
  onItemClick: (item: MenuItem) => void;
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedIndex(0);
  }, [items]);

  // Auto-scroll to keep selected item in view
  useEffect(() => {
    if (selectedIndex >= 0 && containerRef.current) {
      const container = containerRef.current;
      const selectedItem = container.children[selectedIndex] as HTMLElement;
      
      if (selectedItem) {
        const containerHeight = container.clientHeight;
        const itemTop = selectedItem.offsetTop;
        const itemHeight = selectedItem.offsetHeight;
        const scrollTop = container.scrollTop;
        
        // Check if item is above visible area
        if (itemTop < scrollTop) {
          container.scrollTop = itemTop;
        }
        // Check if item is below visible area
        else if (itemTop + itemHeight > scrollTop + containerHeight) {
          container.scrollTop = itemTop + itemHeight - containerHeight;
        }
      }
    }
  }, [selectedIndex]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return;

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : items.length - 1);
          break;
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => prev < items.length - 1 ? prev + 1 : 0);
          break;
        case 'Enter':
          event.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < items.length) {
            onItemClick(items[selectedIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedIndex, items, onItemClick, onClose]);

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      style={{ zIndex: 1300 }}
      modifiers={[{ name: 'preventOverflow', options: { boundary: 'viewport' } }]}
      disablePortal={false} 
    >
      <ClickAwayListener onClickAway={onClose}>
        <Paper 
          ref={containerRef}
          className={styles.searchResultMenuPaper} 
          elevation={3}
        >
          {items.length === 0 ? (
            <div style={{ padding: 8, fontStyle: 'italic' }}>No results</div>
          ) : (
            items.map((item, index) => (
              <div
                className={`${styles.searchResultMenuItem} ${selectedIndex === index ? styles.selected : ''}`}
                key={item.id}
                onClick={() => onItemClick(item)}
              >
                <span className={styles.searchResultMenuItemLabel}>{item.label}</span>
                <span className={styles.searchResultMenuItemDateTime}>{item.date}</span>
              </div>
            ))
          )}
        </Paper>
      </ClickAwayListener>
    </Popper>
  );
};

export default SearchResultMenu;
