import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const userSchema = yup.object().shape({
  // Placeholder for User tab fields
  firstName: yup.string().trim().required('First name is required'),
  lastName: yup.string().trim().required('Last name is required'),
  email: yup.string().email('Invalid email format').required('Email is required') .test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),  
  phoneNumber: yup.string().test('phone-digits', 'Phone number must have at least 10 digits', function(value) {
    if (!value) return true; // Let required validation handle empty values
    const digitCount = (value.match(/\d/g) || []).length;
    return digitCount >= 10;
  }).required('Phone number is required'),
  searchZipcode: yup.string().trim().min(4, 'Zipcode is not valid').required('Zipcode is required'),
  stateSubscription: yup.array(),
});

export type UserFormData = yup.InferType<typeof userSchema>;
