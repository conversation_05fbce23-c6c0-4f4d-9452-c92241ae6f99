.subscriptionRightSidePanel {
    height: 100vh;
    width: 500px;
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.header {
    background-color: #2abcfb;
    padding: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    min-height: 68px;
    border-radius: 13px 13px 0px 0px;


}

.headerText {
    font-family: Syncopate;
    font-size: 23px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.92px;
    text-align: left;
    color: #0f0f14;
    text-transform: uppercase;
}

.content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.textContent {
    display: flex;
    flex-direction: column;
    gap: 16px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #dbdcde;

}

.paragraph {
    color: white !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    margin: 0 !important;
}

.dropdownSection {
    margin-top: 16px;
}

.formControl {
    width: 100%;
}

.contentMain {
    border-radius: 0px 0px 13px 13px;
    border: solid 1px #2abcfb;
    border-top: 0px;
    background-color: #191a20;
    height: calc(100vh - 68px);
}

.select {
    background-color: #333333 !important;
    border-radius: 8px !important;
    color: white !important;

    & .MuiSelect-select {
        color: white !important;
        padding: 12px 16px !important;
        font-weight: bold !important;
    }

    & .MuiOutlinedInput-notchedOutline {
        border: none !important;
    }

    & .MuiSelect-icon {
        color: white !important;
    }
}

.menuPaper {
    background-color: #333333 !important;
    border: 1px solid #555555 !important;

    & .MuiMenuItem-root {
        color: white !important;
        padding: 12px 16px !important;

        &:hover {
            background-color: #444444 !important;
        }

        &.Mui-selected {
            background-color: #0066cc !important;
        }
    }
}

.placeholderText {
    color: white !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

.menuItemText {
    color: white !important;
    font-size: 14px !important;
}

.actionSection {
    margin-top: auto;
    padding-top: 16px;
}

.doLaterButton {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    min-width: auto !important;
    text-transform: none !important;
    box-shadow: none !important;

    &:hover {
        background: none !important;
        box-shadow: none !important;
    }
}

.doLaterText {
    color: #cccccc !important;
    font-size: 14px !important;
    text-decoration: underline;
    cursor: pointer;

    &:hover {
        color: #ffffff !important;
    }
}