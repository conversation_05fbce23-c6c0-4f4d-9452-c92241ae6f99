import { isEmail } from 'src/renderer2/helper';
import * as yup from 'yup';

export const paymentSchema = yup.object().shape({
    achCheckBox: yup.boolean(),
    bankName1: yup.string().required().trim().test("isRequired", "ACH Credit is not valid", function (value) {
        const achCheckBox = this.parent.achCheckBox;
        if (achCheckBox === false) return true;
        return !!value;
    }),
    routingNo: yup.string().required().test("isRequired", "ACH Credit is not valid", function (value) {

        const achCheckBox = this.parent.achCheckBox;
        if (achCheckBox === false) return true;
        if (value && value.includes("x")) {
            return true;
        }
        if (!/^x{5}\d{4}$|^\d{9}$/.test(value)) {
            return false
        }
        return !!value;
    }),
    accountNo: yup.string().required().test("isRequired", "ACH Credit is not valid", function (value) {
        const achCheckBox = this.parent.achCheckBox;
        if (achCheckBox === false) return true;
        if (!/^x+\d{4}$|^\d+$/.test(value)) {
            return false
        }
        return !!value;
    }),
    remittanceEmail: yup.string().email('Invalid email format').required().test('is-email', 'Invalid email format', function (value) {
        if (!value) return true;
        return isEmail(value);
    }),
});

export type paymentFormData = yup.InferType<typeof paymentSchema>;
