import { create } from "zustand";
import { usePostFetchNotifications} from "src/renderer2/hooks/usePostFetchNotifications";

export type NotificationItem = {
    id: string;
    notification_title: string;
    message: string;
    notification_route: string;
    is_read_in_app: boolean;
    created_date: string; // "YYYY-MM-DD HH:mm:ss"
};

type StoreState = {
    anchorEl: HTMLElement | null;
    open: boolean;
    loading: boolean;
    notifications: NotificationItem[];
    setAnchor: (el: HTMLElement | null) => void;
    toggleOpen: (open?: boolean) => void;
    load: () => Promise<void>;
    markAsRead: (id: string) => void;
    markAllRead: () => void;
    clearAll: () => void;
    removeOne: (id: string) => void;
  };
  
  export const useNotificationStore = create<StoreState>((set, get) => ({
    anchorEl: null,
    open: false,
    loading: false,
    notifications: [],
    setAnchor: (el) => set({ anchorEl: el }),
    toggleOpen: (o) => set({ open: typeof o === "boolean" ? o : !get().open }),
    load: async () => {
      set({ loading: true });
      try {
        const res = await usePostFetchNotifications();
        set({ notifications: res.data });
      } finally {
        set({ loading: false });
      }
    },
    markAsRead: (id) =>
      set((s) => ({
        notifications: s.notifications.map((n) =>
          n.id === id ? { ...n, is_read_in_app: true } : n
        ),
      })),
    markAllRead: () =>
      set((s) => ({
        notifications: s.notifications.map((n) => ({
          ...n,
          is_read_in_app: true,
        })),
      })),
    clearAll: () => set({ notifications: [] }),
    removeOne: (id) =>
      set((s) => ({
        notifications: s.notifications.filter((n) => n.id !== id),
      })),
  }));