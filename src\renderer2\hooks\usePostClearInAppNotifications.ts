import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { commomKeys } from "@bryzos/giss-ui-library";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";

const ENDPOINT =
  `${import.meta.env.VITE_API_NOTIFICATION_SERVICE}/notification/in-app-clear-notification`;

export default function usePostClearInAppNotifications() {
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();

  return useMutation(async (ids: string[]) => {
    try {
      const res = await axios.post(
        ENDPOINT,
        { data: {
          notification_id: ids,
          clear_all: ids.length === 0,
        } }
      );
      // Treat any truthy response as success; adjust if backend returns a specific shape
      return !!res.data;
    } catch (error: any) {
      showCommonDialog(
        null,
        commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      throw new Error(error?.message);
    }
  });
}
