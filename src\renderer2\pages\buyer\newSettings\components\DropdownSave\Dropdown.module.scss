.buttonContainer {
    display: flex;
    align-items: center;
    width: 84px;
    height: 36px;
    border-radius: 500px;
    background-color: rgba(255, 255, 255, 0.04);
    overflow: hidden;
    opacity: 1;
    cursor: pointer;
    transition: opacity 0.2s ease;
    border: 1px solid transparent;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
     
}

.saveButton {
    flex: 1;
    height: 100%;
    color: white;
    border: none;
    border-radius: 500px;
    padding: 5px 6px;
    cursor: pointer;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
    color: #fff;
    transition: background-color 0.2s ease;

    &:disabled {
        cursor: not-allowed;
    }

    &:not(:disabled):hover{
        background-color: #2abcfb;
        color: #0f0f14;
    }

    &:focus{
        border: 0.5px solid #fff;
        color: #fff
      }
   
}

.dropdownButton {
    border: none;
    border-left: solid 1px rgba(255, 255, 255, 0.1);
    width: 30px;
    padding: 8px 5px;
    height: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;

    svg {
        width: 18px;

        path {
            fill: #fff;
            fill-opacity: unset;
        }
    }

    &.dropdownButtonActive {
        svg {
            transform: rotate(180deg);
        }

    }

    &:disabled {
        cursor: not-allowed;
    }

}

.dropdownIcon {
    transition: transform 0.2s ease;
}

.dropdownMenu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 2px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 4px;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #9c9da5;
    width: 116px;
}

.dropdownItem {
    width: 100%;
    padding: 6px 10px;
    border: none;
    cursor: pointer;
    font-family: Inter;
    font-size: 14px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: #191a20;
    transition: background-color 0.2s ease;
    border-radius: 6px;

    &:hover {
        background-color: #e0e0e0;
        font-weight: bold;

    }
}