.input{
    height: 50px;
    width: 256px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid transparent;
    gap: 20px;
    padding: 0 12px 0 24px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 15px;
    letter-spacing: 0.6px;
    text-align: left;
    outline: none;
    caret-color: #fff;
    color:var(--W--01);
    transition: all 0.1s;
    &::placeholder{
      color: #616575;
    }
    
    &:focus{
      outline: none;
      border-style: solid;
      color: #fff;
    }

    &.inputError{
        border: 0px;
        border-image-source: linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
        background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
        background-origin: border-box;
        background-clip: border-box, border-box;
        z-index: 2;
        color: #fff !important;
        &:focus{
            color: #fff;
            box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
        }
        &::placeholder{
            color: white !important;
        }
    }
  }
