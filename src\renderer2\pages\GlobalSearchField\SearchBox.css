.search-container {
    position: relative;
    display: flex;
    flex: 1;
    justify-content: center;
  }
  
  .search-button-and-field {
    display: flex;
    align-items: center;
  }
  
  .search-type-btn {
    background-color: #2c2c2c;
    color: #fff;
    padding: 8px 12px;
    border: none;
    border-radius: 20px 0 0 20px;
    cursor: pointer;
  }

  .subscribe-btn {
    background-color: #10be0a;
    color: #fff;
    padding: 8px 12px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
  }
  
  .search-box-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 0 20px 20px 0;
    padding: 6px 12px;
  }
  
  .search-icon,
  .right-icon {
    font-size: 16px;
    color: #888;
    padding: 0 4px;
  }
  
  .search-input {
    border: none;
    outline: none;
    padding: 6px;
    flex: 1;
    min-width: 200px;
  }
  
  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 2px;
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 10;
    width: 100%;
  }
  
  .dropdown-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
  }
  
  .dropdown-item:last-child {
    border-bottom: none;
  }
  