// @ts-nocheck
import React, { useState } from 'react';
import styles from './SubscribeAdmin.module.scss';
import SearchHeader from '../../pages/SearchHeader';
import clsx from 'clsx';
import { Select, MenuItem, FormControl } from '@mui/material';

const SubscribeAdmin = () => {
  const assignedLicenses = 5;
  const maxRows = 11;

  const groupName = [
    { label: 'Group A', value: 'User1' },
    { label: 'Group B', value: 'User2' }
  ];

  const actions = [
    { label: 'Unassign License', value: 'unassign' },
    { label: 'Unassign & Remove User From List', value: 'unassign_remove' },
    { label: 'Move User to a Different Group', value: 'move_group' },
    { label: 'Make User an Account Admin', value: 'make_admin' },
  ];

  const [users, setUsers] = useState(
    Array.from({ length: assignedLicenses }, (_, index) => ({
      id: index ,
      name: '',
      email: '',
      group: '',
      action: '',
      status: 'Active',
      isFocused: false,
    }))
  );

  const handleUserChange = (index, field, value) => {
    const updated = [...users];
    updated[index][field] = value;
    setUsers(updated);
  };

  const handleFocusChange = (index, isFocused) => {
    const updated = [...users];
    updated[index].isFocused = isFocused;
    setUsers(updated);
  };

  return (
    <div className={styles.subscribeAdminPage}>
      <div className={styles.subscribeAdminPageHeader}>
        You have <span>{assignedLicenses}</span> licenses assigned to <span>1</span> users
      </div>

      <div className={styles.subscribeAdminPageContent}>
        <div className={styles.subscribeAdminPageContentHeader}>
          <span className={clsx(styles.lblList, styles.lblName)}>NAME</span>
          <span className={clsx(styles.lblList, styles.lblGroup)}>GROUP</span>
          <span className={clsx(styles.lblList, styles.lblEmail)}>EMAIL ADDRESS</span>
          <span className={clsx(styles.lblList, styles.lblStatus)}>STATUS</span>
          <span className={clsx(styles.lblList, styles.lblAction)}>ACTION</span>
        </div>

        <div className={styles.subscribeAdminPageContentBody}>
          {Array.from({ length: maxRows }, (_, index) => {
            const isInteractive = index < assignedLicenses;
            const user = users[index] || {};

            return (
              <div
                key={`row-${index}`}
                className={clsx(
                  styles.subscribeAdminPageContentBodyItem,
                  user?.isFocused && styles.focusedContainer
                )}
              >
                <span className={styles.subscribeAdminPageContentBodyItemNumber}>{index + 1}</span>

                {isInteractive ? (
                  <>
                    <span className={styles.nameInput}>
                      <input
                        value={user.name}
                        onChange={(e) => handleUserChange(index, 'name', e.target.value)}
                        onFocus={() => handleFocusChange(index, true)}
                        onBlur={() => handleFocusChange(index, false)}
                      />
                    </span>

                    <span className={styles.inputGroup}>
                      <FormControl fullWidth className={styles.dropdownFormControl}>
                        <Select
                          value={user.group}
                          onChange={(e) => handleUserChange(index, 'group', e.target.value)}
                          displayEmpty
                          classes={{
                            root: clsx(styles.dropdownMain, styles.groupDropdownMain),
                            outlined: styles.selectDropdown
                          }}
                          MenuProps={{
                            classes: { paper: styles.selectDropdownList },
                            anchorOrigin: { vertical: 27, horizontal: 'right' },
                            transformOrigin: { vertical: 'top', horizontal: 'right' }
                          }}
                          renderValue={(selected) => {
                            if (!selected)
                              return <span style={{ color: 'rgba(255, 255, 255, 0.6)' }}>-</span>;
                            const selectedGroup = groupName.find((g) => g.value === selected);
                            return selectedGroup?.label || '';
                          }}
                        >
                          {groupName.map((g) => (
                            <MenuItem key={g.value} value={g.value} className={styles.menuItem}>
                              {g.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </span>

                    <span className={styles.inputEmail}>
                      <input
                        value={user.email}
                        onChange={(e) => handleUserChange(index, 'email', e.target.value)}
                        onFocus={() => handleFocusChange(index, true)}
                        onBlur={() => handleFocusChange(index, false)}
                      />
                    </span>

                    <span className={styles.inputStatus}>{user.status}</span>

                    <span>
                      <FormControl fullWidth className={styles.dropdownFormControl}>
                        <Select
                          value={user.action}
                          onChange={(e) => handleUserChange(index, 'action', e.target.value)}
                          displayEmpty
                          classes={{
                            root: styles.dropdownMain,
                            outlined: styles.selectDropdown
                          }}
                          MenuProps={{
                            classes: { paper: styles.selectDropdownList },
                            anchorOrigin: { vertical: 27, horizontal: 'right' },
                            transformOrigin: { vertical: 'top', horizontal: 'right' }
                          }}
                          renderValue={(selected) => {
                            if (!selected)
                              return <span style={{ color: 'rgba(255, 255, 255, 0.6)' }}>Select Action</span>;
                            const selectedAction = actions.find((a) => a.value === selected);
                            return selectedAction?.label || '';
                          }}
                        >
                          {actions.map((a) => (
                            <MenuItem key={a.value} value={a.value} className={styles.menuItem}>
                              {a.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </span>
                  </>
                ) : (
                  <>
                    <span className={styles.nameInput}></span>
                    <span className={styles.inputGroup}></span>
                    <span className={styles.inputEmail}></span>
                    <span className={styles.inputStatus}></span>
                    <span></span>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <button className={styles.subscribeAdminPageButton}>
        <span>DONE</span>
      </button>
    </div>
  );
};

export default SubscribeAdmin;
