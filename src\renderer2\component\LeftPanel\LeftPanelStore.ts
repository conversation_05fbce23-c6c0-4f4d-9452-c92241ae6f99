// @ts-nocheck
import { localStorageKeys } from 'src/renderer2/common';
import { getLocal, setLocal } from 'src/renderer2/helper';
import { create } from 'zustand';

interface MenuState {
  openLeftPanel: boolean;
  closeWithoutAnimation: boolean;
  leftPanelData: any;
  displayLeftPanel: boolean;
  clickedCreateNewButton: any;
  setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => void;
  resetLeftPanelStore: () => void;
  setOpenLeftPanel: (openLeftPanel: boolean) => void;
  setLeftPanelData: (leftPanelData: any) => void; 
  setDisplayLeftPanel: (displayLeftPanel: boolean) => void;
  setClickedCreateNewButton: (clickedCreateNewButton: any) => void;
}

const commonStore = {
    openLeftPanel:false,
    closeWithoutAnimation:false,
    leftPanelData: null,
    displayLeftPanel: false,
    leftPanelViewPoHistoryData: null,
    clickedCreateNewButton: null
}
  
  
  export const useLeftPanelStore = create<MenuState>((set, get) => ({
    ...commonStore,
    setOpenLeftPanel: (openLeftPanel: boolean) => set({ openLeftPanel }),
    setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => set({ closeWithoutAnimation }),
    setLeftPanelData: (leftPanelData: any) => set({ leftPanelData }),
    setDisplayLeftPanel: (displayLeftPanel: boolean) => set({ displayLeftPanel }),
    setLeftPanelViewPoHistoryData: (leftPanelViewPoHistoryData: any) => set({ leftPanelViewPoHistoryData }),
    setClickedCreateNewButton: (clickedCreateNewButton: any) => set({ clickedCreateNewButton }),
    resetLeftPanelStore: () => set(state => ({
      ...commonStore
    })),
  }));
    