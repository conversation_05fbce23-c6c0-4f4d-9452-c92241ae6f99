import React, { useEffect, useRef } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import styles from './SubscribeUserTable.module.scss';
import {
  subscribeUserTableSchema,
  type SubscribeUserTableFormData,
  type User
} from './SubscribeUserTable.schema';
import { useGlobalStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import usePostSaveUserListSubscription from 'src/renderer2/hooks/usePostSaveUserListSubscription';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { MenuItem, Select } from '@mui/material';
import clsx from 'clsx';

interface SubscribeUserTableProps {
  initialUsers?: User[];
  onUserUpdate?: (users: User[]) => void;
  setSaveFunctions?: any;
}

const SubscribeUserTable: React.FC<SubscribeUserTableProps> = ({
  onUserUpdate,
  setSaveFunctions
}) => {
    const {
    control,
    handleSubmit,
    formState: { errors, isDirty, isSubmitted },
    watch,
    setValue,
    clearErrors,
    reset ,
    setError
  } = useForm<SubscribeUserTableFormData>({
    resolver: yupResolver(subscribeUserTableSchema),
    mode: 'onSubmit', // Only validate on submit
    reValidateMode: 'onSubmit', // Keep validation only on submit even after first submit
    defaultValues: {
      users: []
    }
  });

  const { fields, update, append, replace } = useFieldArray({
    control,
    name: 'users'
  });

  const { userList, uploadUserList, setUploadUserList, userSubscription , subscriptionDialogOpen} = useSubscriptionStore();
  const { setShowLoader } = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const {
    mutateAsync: postSaveUserListSubscription
  } = usePostSaveUserListSubscription();

  // Store original data for comparison
  const originalDataRef = useRef<User[]>([]);

  // Get license counts from subscription store
  const totalLicenses = userSubscription?.licenses?.current_total || 0;



  // Set initial user data from store
  useEffect(() => {
    if (userList?.length > 0) {
      const transformedUsers = userList.map((user: any) => ({
        ...user,
        isExisting: true
      }));

      // Store deep copy of original data for comparison
      originalDataRef.current = JSON.parse(JSON.stringify(transformedUsers));

      // Always add placeholder row at the end
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        licensed: false,
        action: '',
        status: '',
        isExisting: false,
        isRemoved: false
      };

      const newUsers = [...transformedUsers, placeholderUser];
      // Use reset for initial data to avoid marking as dirty
      reset({ users: newUsers });
      // Set the original data reference for comparison
      originalDataRef.current = JSON.parse(JSON.stringify(newUsers));
    } else {
      // If no existing users, just show placeholder row
      originalDataRef.current = [];

      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        licensed: false,
        action: '',
        status: '',
        isExisting: false,
        isRemoved: false
      };

      const newUsers = [placeholderUser];
      // Use reset for initial data to avoid marking as dirty
      reset({ users: newUsers });
    }
  }, [userList, reset]);


  useEffect(() => {
    if(setSaveFunctions){
    setSaveFunctions({
            onSave: () => handleSubmit(onSubmit)(),
            isDisabled: !isDirty,
        });
    }
}, [isDirty, handleSubmit, setSaveFunctions]);


  // Handle uploaded users from store
  useEffect(() => {
    if (uploadUserList?.length > 0) {
      const currentUsers = watch('users') || [];
      
      // Filter out users whose email already exists in the current list
      const filteredUploadedUsers = uploadUserList.filter((uploadedUser: any) => {
        const emailExists = currentUsers.some(existingUser => 
          existingUser.email_id?.toLowerCase() === uploadedUser.email_id?.toLowerCase()
        );
        return !emailExists;
      });

      if (filteredUploadedUsers.length > 0) {
        const transformedUploadedUsers = filteredUploadedUsers.map((user: any) => ({
          id: `new-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // Generate temporary ID for form handling
          user_name: user.user_name || '',
          email_id: user.email_id || '',
          licensed: user.license || false, // Keep as boolean
          action: user.status || '', // Use status as action or empty string
          status: '',
          isExisting: false, // Treat uploaded users as new users (placeholder rows)
          isRemoved: false
        }));

        // Get existing users (excluding placeholder rows)
        const existingUsers = currentUsers.filter(user => 
          user.isExisting || (user.user_name?.trim() && user.email_id?.trim())
        );

        // Keep the final placeholder row at the end
        const placeholderUser: User = {
          id: `new-${Date.now()}`,
          user_name: '',
          email_id: '',
          licensed: false,
          action: '',
          status: '',
          isExisting: false,
          isRemoved: false
        };

        // Merge existing users with new uploaded users and placeholder
        const newUsers = [...existingUsers, ...transformedUploadedUsers, placeholderUser];
        
        // Use replace instead of reset to maintain dirty state
        replace(newUsers);
        // Explicitly mark as dirty since we're setting new data
        setValue('users', newUsers, { shouldDirty: true });
      }
      
      setUploadUserList(null);
    }
  }, [uploadUserList, replace, setValue, setUploadUserList, watch]);

  const watchedUsers = watch('users') || [];


  // Calculate current assigned licenses from watched users
  const currentAssignedLicenses = watchedUsers.filter(user => user.licensed).length;

  // Calculate how many more licenses can be assigned
  const remainingLicenses = totalLicenses - currentAssignedLicenses;

  // Add new placeholder row when email field is filled and blurred
  const handleEmailBlur = (index: number) => {
    const currentUser = watchedUsers[index];

    // Only add new placeholder if this is a placeholder row that now has both username and email
    if (currentUser && !currentUser.isExisting &&
        currentUser.user_name?.trim() && currentUser.email_id?.trim()) {

      // Check if there's already another empty placeholder row
      const hasEmptyPlaceholder = watchedUsers.some(user =>
        !user.isExisting && !user.user_name?.trim() && !user.email_id?.trim()
      );

      if (!hasEmptyPlaceholder) {
        const newPlaceholder: User = {
          id: `new-${Date.now()}`,
          user_name: '',
          email_id: '',
          licensed: false,
          action: '',
          status: '',
          isExisting: false,
          isRemoved: false
        };

        append(newPlaceholder);
      }
    }
  };

  // Handle license changes
  const handleLicenseChange = (index: number, newLicense: 'Assigned' | 'Unassigned') => {
    if (watchedUsers[index]) {
      const currentUser = watchedUsers[index];
      
      // Prevent assigning more licenses than available
      if (newLicense === 'Assigned' && !currentUser.licensed) {
        if (remainingLicenses <= 0) {
          // Cannot assign more licenses
          return;
        }
      }
      
      const updatedUser = { ...currentUser, licensed: newLicense === 'Assigned' };
      update(index, updatedUser);
      onUserUpdate?.(watchedUsers as User[]);
    }
  };

  // Handle action changes
  const handleActionChange = (index: number, action: string) => {
    if (!watchedUsers[index]) return;

    const updatedUser = {
      ...watchedUsers[index],
      action,
      // Mark as removed if action is "Remove", but keep in data for backend
      isRemoved: action === 'REMOVE'
    };

    update(index, updatedUser);
    onUserUpdate?.(watchedUsers as User[]);
  };



  // Utility functions
  const isPlaceholderRow = (user: any): boolean => {
    return !user.isExisting && !user.user_name?.trim() && !user.email_id?.trim();
  };

  const isFieldDisabled = (user: any, field: 'user_name' | 'email_id' | 'license' | 'action'): boolean => {
    if (user.isExisting) return false;

    // For new users, username and email are always enabled for tab navigation
    if (field === 'user_name' || field === 'email_id') return false;

    // For new users, disable action buttons and license until username is filled
    if (field === 'action' || field === 'license') {
      return !user.user_name?.trim();
    }

    return false;
  };

  // Check if license field should be disabled due to license limit
  const isLicenseDisabled = (user: any): boolean => {
    // If this is an existing user with an assigned license, allow them to keep it
    if (user.isExisting && Boolean(user.licensed)) {
      return false;
    }

    // If trying to assign a license and no more are available, disable
    // For existing users who are unassigned, also disable if no licenses available
    if (!Boolean(user.licensed) && remainingLicenses <= 0) {
      return true;
    }

    return false;
  };

  // Utility function to compare if user data has changed
  const hasUserChanged = (currentUser: any, originalUser: User | undefined): boolean => {
    if (!originalUser) return true; // New user

    return (
      currentUser.user_name !== originalUser.user_name ||
      currentUser.email_id !== originalUser.email_id ||
      Boolean(currentUser.licensed) !== Boolean(originalUser.licensed) ||
      currentUser.action !== originalUser.action ||
      currentUser.status !== originalUser.status
    );
  };

  // Form submission with payload filtering and change detection
  const onSubmit = async (data: SubscribeUserTableFormData) => {
    try {
      setShowLoader(true);
      
      // Check for duplicate emails before processing
      const currentUsers = watch('users') || [];
      const emailCounts: { [key: string]: number[] } = {};
      
      // Count occurrences of each email and track their indices
      currentUsers.forEach((user, index) => {
        if (user.email_id?.trim()) {
          const email = user.email_id.trim().toLowerCase();
          if (!emailCounts[email]) {
            emailCounts[email] = [];
          }
          emailCounts[email].push(index);
        }
      });
      
      // Check for duplicates and set errors
      let hasDuplicateEmails = false;
      Object.entries(emailCounts).forEach(([email, indices]) => {
        if (indices.length > 1) {
          hasDuplicateEmails = true;
          // Set error for all duplicate email fields
          indices.forEach(index => {
            setError(`users.${index}.email_id`, { 
              message: 'Duplicate email addresses are not allowed' 
            });
          });
        }
      });
      
      // If duplicates found, stop submission and show error
      if (hasDuplicateEmails) {
        setShowLoader(false);
        return;
      }
      
      const processedUsers: any[] = [];

      (data.users || []).forEach(user => {
        const userTyped = user as User;

        // Skip empty placeholder rows (but not removed users)
        if (!userTyped.isExisting && !userTyped.isRemoved && (!userTyped.user_name?.trim() || !userTyped.email_id?.trim())) {
          return;
        }

        if (userTyped.isExisting) {
          // For existing users, find original and check if changed OR if removed
          const originalUser = originalDataRef.current.find(orig => orig.id === userTyped.id);

          if (userTyped.isRemoved || hasUserChanged(userTyped, originalUser)) {
            // Include id for existing users and send data (including removed ones)
            processedUsers.push({
              id: userTyped.id,
              user_name: userTyped.user_name,
              email_id: userTyped.email_id,
              licensed: userTyped.licensed,
              action: userTyped.action || undefined,
              status: userTyped.status || undefined
            });
          }
        } else {
          // For new users, don't include id and only include if both fields are filled
          // Skip new users with "Remove" action - they don't exist yet, so nothing to remove
          if (userTyped.user_name?.trim() && userTyped.email_id?.trim() && userTyped.action !== 'REMOVE') {
            processedUsers.push({
              user_name: userTyped.user_name.trim(),
              email_id: userTyped.email_id.trim(),
              licensed: userTyped.licensed,
              action: userTyped.action || undefined,
              status: userTyped.status || undefined
            });
          }
        }
      });

      const payload = { data: processedUsers };
      const response = await postSaveUserListSubscription(payload);
      if (
        typeof response === "object" &&
        "error_message" in response
      ) {
        const { reason, email: emails } = response.error_message;
                 if (reason === 'invalid_domain'){
           const currentUsers = watch('users') || [];
           emails.forEach((email: any) => {
             const userIndex = currentUsers.findIndex((user: any) => user.email_id === email);
             if (userIndex !== -1) {
               setError(`users.${userIndex}.email_id`, { message: 'User must have same company domain' });
             }
           });
         }
        else{
          showCommonDialog(null, response.error_message || 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
        }
        setShowLoader(false);
      } else {
        showCommonDialog(null, 'Account updated successfully', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      }
         } catch (error) {
       setShowLoader(false);
       showCommonDialog(null, 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
     } finally{
      setShowLoader(false);
     }
  };

  // Render editable cell for existing users
  const renderEditableCell = (index: number, field: 'user_name' | 'email_id') => {
    const fieldName = `users.${index}.${field}` as const;
    const error = errors.users?.[index]?.[field];
    const user = watchedUsers[index];

    // Only show errors after form submission attempt
    const shouldShowError = error && isSubmitted;

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Tab') {
        // Let normal tab behavior work - the useEffect will handle adding new placeholder rows
        return;
      }
    };

    return (
      <div className={styles.cellContainer}>
        <Controller
          name={fieldName}
          control={control}
          render={({ field: controllerField }) => (
            <input
              type="text"
              {...controllerField}
              onChange={(e) => {
                controllerField.onChange(e);
                // Clear errors when user starts typing to prevent blur validation
                if (shouldShowError) {
                  clearErrors(`users.${index}.${field}`);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                // Add new placeholder row when email field is blurred and filled
                if (field === 'email_id') {
                  handleEmailBlur(index);
                }
              }}
              onKeyDown={handleKeyDown}
              disabled={isFieldDisabled(user, field)}
              className={`${styles.editInput} ${shouldShowError ? styles.error : ''} ${
                isPlaceholderRow(user) ? styles.placeholder : ''
              } ${isFieldDisabled(user, field) ? styles.disabled : ''}`}
              placeholder={field === 'user_name' ? 'Enter user name' : 'Enter email'}
              tabIndex={0}
            />
          )}
        />
      </div>
    );
  };

  return (
    <div style={{height: '100%'}}>
      <div className={clsx(styles.tableCard, !userSubscription?.subscription_id && styles.disableUserTable)}>
        <table className={styles.table}>
          <thead className={styles.tableHeader}>
            <tr className={styles.tableHeaderRow}>
              <th>USER NAME</th>
              <th>Email</th>
              <th>LICENSE</th>
              <th>ACTION</th>
              <th>STATUS</th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => {
              const user = watchedUsers[index];
              const isPlaceholder = isPlaceholderRow(user);

              // Hide removed users from display
              if ((user as User)?.isRemoved) {
                return null;
              }

              return (
                <tr
                  key={field.id}
                  className={`${styles.tableRow} ${isPlaceholder ? styles.placeholderRow : ''}`}
                >
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'user_name')}
                  </td>
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'email_id')}
                  </td>
                  <td className={styles.tableCell}>
                      <Select
                        MenuProps={{
                              classes: {
                                  paper: styles.Dropdownpaper,
                                  list: styles.muiMenuList,
                              },
                          }}
                        value={user?.licensed ? 'Assigned' : 'Unassigned'}
                        onChange={(e) => handleLicenseChange(index, e.target.value as 'Assigned' | 'Unassigned')}
                        disabled={isFieldDisabled(user, 'license') || isLicenseDisabled(user)}
                        className={`${styles.selectDropdown} ${
                          user?.licensed ? styles.assigned : styles.unassigned
                        } ${(isFieldDisabled(user, 'license') || isLicenseDisabled(user)) ? styles.disabled : ''}`}
                      >
                        <MenuItem value="Assigned">Assigned</MenuItem>
                        <MenuItem value="Unassigned">Unassigned</MenuItem>
                      </Select>
                  </td>
                  <td className={styles.tableCell}>
                    <Select
                    disableUnderline
                      value={user?.action || 'Select Action'}
                      onChange={(e) => {
                        if (e.target.value !== 'Select Action') {
                          handleActionChange(index, e.target.value);
                        }
                      }}
                        MenuProps={{
                              classes: {
                                  paper: styles.Dropdownpaper,
                                  list: styles.muiMenuList,
                              },
                          }}
                      disabled={isFieldDisabled(user, 'action')}
                      className={`${styles.selectDropdown} ${isFieldDisabled(user, 'action') ? styles.disabled : ''}`}
                    >
                      <MenuItem value="Select Action">Select Action</MenuItem>
                      <MenuItem value="INVITE">Invite</MenuItem>
                      <MenuItem value="REMOVE">Remove</MenuItem>
                    </Select>
                  </td>
                  <td className={styles.tableCell}>
                    <span className={styles.status}>
                      {user?.status || ''}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      {
        subscriptionDialogOpen && (
      <div className={styles.saveButtonContainer}>
        <button
          type="submit"
          disabled={!isDirty}
          onClick={handleSubmit(onSubmit)}
          className={`${styles.saveButton} ${!isDirty ? styles.disabled : ''}`}
        >
          Save Changes
        </button>
      </div>
      )
      }
    </div>
  );
};

export default SubscribeUserTable;
