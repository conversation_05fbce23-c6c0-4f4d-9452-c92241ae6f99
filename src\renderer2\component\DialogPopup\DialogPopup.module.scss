.ErrorDialog.ErrorDialog {
    z-index: 1301;
    .dialogContent {
        max-width: 504px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 60px 40px 48px 40px;
        border-radius: 50px;
        background-color: #0f0f14;
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        box-shadow: none;
        z-index: 9999;

        .closeIcon {
            position: absolute;
            top: 10px;
            right: 12px;
            cursor: pointer;
            opacity: 0.5;

            &:hover,
            &:focus {
                opacity: unset;
            }

            svg {
                height: 20px;
                width: 20px;
                color: white;

                path {
                    fill: #fff
                }
            }
        }

        .successPopupTitle {
            font-family: Syncopate;
            font-size: 24px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.96px;
            text-align: center;
            color: #fff;
        }

        p {
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: -0.96px;
            text-align: center;
            color: #fff;
            text-transform: uppercase;
            margin-bottom: 16px;
        }

        .DialogPopupBtn {
            display: flex;
            flex-direction: column;
            margin-top: 48px;
            row-gap: 20px;
            width: 50%;
        }

        .submitBtn {
            width: 100%;
            height: 69px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px 11px;
            border-radius: 50px;
            background-color: #32ff6c;
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: center;
            color: #000;
            transition: all 0.1s;
            text-transform: uppercase;

            span{
                display: flex;
                flex-direction: column;
                span{
                    font-family: Syncopate;
                    font-size: 18px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.72px;
                    text-align: center;
                    color: #000;
                    &:last-child{
                        font-weight: normal;
                    }
                }
            }

            &:last-child {
                margin-right: 0px;
                background-color: #2b2c32;
                color: #fff;
                 span{
               
                span{
                  
                    color: #fff;
                    
                }
            }
            }

            // &:hover,
            // &:focus {
            //     background-color: #70ff00;
            //     border: solid 0.5px #70ff00;
            //     color: #000;
            // }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }


    }

}