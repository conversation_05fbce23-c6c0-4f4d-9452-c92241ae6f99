import React, { useEffect, useMemo, useRef, useState } from 'react'
import styles from './ListTab.module.scss';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { MenuItem, Select } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { localStorageKeys, routes } from 'src/renderer2/common';
import SavedPricingTemplate from '../Templates/SavedPricingTemplate';
import clsx from 'clsx';
import { useLeftPanelStore } from '../LeftPanelStore';
import { noIdGeneric, useBuyerSettingStore, useCreatePoStore, useOrderManagementStore, useSearchStore } from '@bryzos/giss-ui-library';
import { clearLocal, convertDateFormat, fetchPrice, getLocal, navigatePage, newPriceFormatter } from 'src/renderer2/helper';
import usePostSaveSearchProducts from 'src/renderer2/hooks/usePostSaveSearchProducts';
import { ReactComponent as CreateNewButton } from '../../../assets/New-images/New-Image-latest/create-new-button.svg';
import { ReactComponent as CreateNewButtonHover } from '../../../assets/New-images/New-Image-latest/create-new-button-hover.svg';
import { ReactComponent as DeleteIcon } from '../../../assets/New-images/New-Image-latest/delete-outlined.svg';
import { ReactComponent as DropdownArrow } from '../../../assets/New-images/New-Image-latest/Polygon.svg';
import { ReactComponent as CreateNewPluseIcon } from '../../../assets/New-images/New-Image-latest/createNewPluse.svg';
import { ReactComponent as CreateNewPluseIconHover } from '../../../assets/New-images/New-Image-latest/createNewPluseHover.svg';
import { ReactComponent as LeftSideArrow } from '../../../assets/New-images/New-Image-latest/back-button-icon-container.svg';
import useDeleteSearchProducts from 'src/renderer2/hooks/useDeleteSearchProducts';
import DraftPoTemplate from '../Templates/DraftPoTemplate';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {  useGlobalSearchStore } from 'src/renderer2/pages/GlobalSearchField/globalSearchStore';
import { useBomPdfExtractorStore } from 'src/renderer2/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import usePostCancelDraftPo from 'src/renderer2/hooks/usePostCancelDraftPo';
import OrderManagementTemplate from '../Templates/OrderManagementTemplate';

dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(customParseFormat);

// Define types for better TypeScript support
interface SearchProduct {
  id: string;
  title: string;
  created_date: string;
  time_stamp?: string;
  search_date_time: string;
  item_count: number;
  amount?: number;
  name?: string;
  products: Array<{ shape?: string }>;
  order_size: number;
}

interface GroupedData {
  [key: string]: SearchProduct[];
}

// Unified grouping function that works for all filters
const groupByTimeLabels = (items: SearchProduct[], sortOrder: 'newest' | 'oldest' = 'newest', field: string = 'created_date'): GroupedData => {
  const now = dayjs();
  const groups: GroupedData = {
    'Today': [],
    'Yesterday': [],
    'This Week': [],
    'Last Week': [],
    'This Month': [],
    'Last Month': [],
    'This Year': [],
    'Older': []
  };


  // If no items, return Today group with empty list
  if (!items || items.length === 0) {
    return {
      'Today': []
    };
  }

  items?.forEach((item: SearchProduct) => {
    // Use the specified field for grouping, fallback to created_date if time_stamp is not available
    const dateField = field === 'time_stamp' && item.time_stamp ? item.time_stamp : item.created_date;
    const itemDate = dayjs(convertDateFormat(dateField), "YYYY-MM-DD HH:mm:ss");
    
    if (itemDate.isToday()) {
      groups['Today'].push(item);
    } else if (itemDate.isYesterday()) {
      groups['Yesterday'].push(item);
    } else if (itemDate.isSame(now, 'week')) {
      groups['This Week'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'week'), 'week')) {
      groups['Last Week'].push(item);
    } else if (itemDate.isSame(now, 'month')) {
      groups['This Month'].push(item);
    } else if (itemDate.isSame(now.subtract(1, 'month'), 'month')) {
      groups['Last Month'].push(item);
    } else if (itemDate.isSame(now, 'year')) {
      groups['This Year'].push(item);
    } else {
      groups['Older'].push(item);
    }
  });

  // Remove empty groups
  Object.keys(groups).forEach(key => {
    if (groups[key].length === 0) {
      delete groups[key];
    }
  });

  // If no groups remain after filtering, return Today group with empty list
  if (Object.keys(groups).length === 0) {
    return {
      'Today': []
    };
  }

  // Sort groups based on sortOrder
  const groupOrder = {
    'newest': ['Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month', 'This Year', 'Older'],
    'oldest': ['Older', 'This Year', 'Last Month', 'This Month', 'Last Week', 'This Week', 'Yesterday', 'Today']
  };

  const sortedGroups: GroupedData = {};
  const order = groupOrder[sortOrder];
  
  order.forEach(groupName => {
    if (groups[groupName]) {
      sortedGroups[groupName] = groups[groupName];
    }
  });

  return sortedGroups;
};

// Filter functions for each menu item - all using the same grouping system
const filterByNewest = (data: SearchProduct[], field: string = 'created_date'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest', field);
  
  // Sort each group by the specified field (newest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs(convertDateFormat(a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bCreatedDate = dayjs(convertDateFormat(b.created_date), "YYYY-MM-DD HH:mm:ss");
      const aTimeStamp = dayjs(convertDateFormat(a.time_stamp || a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bTimeStamp = dayjs(convertDateFormat(b.time_stamp || b.created_date), "YYYY-MM-DD HH:mm:ss");
      switch (field) {
        case 'created_date':
          return bCreatedDate.diff(aCreatedDate);
        case 'time_stamp':
          return bTimeStamp.diff(aTimeStamp);
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return bCreatedDate.diff(aCreatedDate);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByOldest = (data: SearchProduct[], field: string = 'created_date'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'oldest', field);
  
  // Sort each group by the specified field (oldest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs(convertDateFormat(a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bCreatedDate = dayjs(convertDateFormat(b.created_date), "YYYY-MM-DD HH:mm:ss");
      const aTimeStamp = dayjs(convertDateFormat(a.time_stamp || a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bTimeStamp = dayjs(convertDateFormat(b.time_stamp || b.created_date), "YYYY-MM-DD HH:mm:ss");
      switch (field) {
        case 'created_date':
          return aCreatedDate.diff(bCreatedDate);
        case 'time_stamp':
          return aTimeStamp.diff(bTimeStamp);
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return aCreatedDate.diff(bCreatedDate);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByAToZ = (data: SearchProduct[], field: string = 'title'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest', field);
  
  // Sort each group alphabetically by the specified field (A to Z)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs(convertDateFormat(a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bCreatedDate = dayjs(convertDateFormat(b.created_date), "YYYY-MM-DD HH:mm:ss");
      const aTimeStamp = dayjs(convertDateFormat(a.time_stamp || a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bTimeStamp = dayjs(convertDateFormat(b.time_stamp || b.created_date), "YYYY-MM-DD HH:mm:ss");
      
      switch (field) {
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'created_date':
          return aCreatedDate.diff(bCreatedDate);
        case 'time_stamp':
          return aTimeStamp.diff(bTimeStamp);
        default:
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByZToA = (data: SearchProduct[], field: string = 'title'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest', field);
  
  // Sort each group alphabetically by the specified field (Z to A)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs(convertDateFormat(a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bCreatedDate = dayjs(convertDateFormat(b.created_date), "YYYY-MM-DD HH:mm:ss");
      const aTimeStamp = dayjs(convertDateFormat(a.time_stamp || a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bTimeStamp = dayjs(convertDateFormat(b.time_stamp || b.created_date), "YYYY-MM-DD HH:mm:ss");
      
      switch (field) {
        case 'title':
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
        case 'created_date':
          return bCreatedDate.diff(aCreatedDate);
        case 'time_stamp':
          return bTimeStamp.diff(aTimeStamp);
        default:
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByHighest = (data: SearchProduct[], field: string = 'amount'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest', field);
  
  // Sort each group by the specified field (highest to lowest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      const aCreatedDate = dayjs(convertDateFormat(a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bCreatedDate = dayjs(convertDateFormat(b.created_date), "YYYY-MM-DD HH:mm:ss");
      const aTimeStamp = dayjs(convertDateFormat(a.time_stamp || a.created_date), "YYYY-MM-DD HH:mm:ss");
      const bTimeStamp = dayjs(convertDateFormat(b.time_stamp || b.created_date), "YYYY-MM-DD HH:mm:ss");
      
      switch (field) {
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return (b.item_count || 0) - (a.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByLowest = (data: SearchProduct[], field: string = 'amount'): GroupedData => {
  const grouped = groupByTimeLabels(data, 'newest', field);
  
  // Sort each group by the specified field (lowest to highest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return (a.item_count || 0) - (b.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const animatedItems: Set<string> = new Set();

const ListTab = () => {
  const location = useLocation();
  const setSelectedSavedSearch = useSearchStore(state => state.setSelectedSavedSearch );
  const setShortListedSearchProductsData = useSearchStore(state => state.setShortListedSearchProductsData);
  const setFilterShortListedSearchProductsData = useSearchStore(state => state.setFilterShortListedSearchProductsData);
  const setSelectedProductsData = useSearchStore(state => state.setSelectedProductsData);
  const setSaveFeedbackMap = useSearchStore(state => state.setSaveFeedbackMap);
  const setFocusSingleProduct = useSearchStore(state => state.setFocusSingleProduct);
  const savedSearchProducts = useSearchStore(state => state.savedSearchProducts);
  const setSavedSearchProducts = useSearchStore(state => state.setSavedSearchProducts);
  const quoteList = useCreatePoStore(state => state.quoteList);
  const setQuoteList = useCreatePoStore(state => state.setQuoteList);
  const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
  const selectedQuote = useCreatePoStore(state => state.selectedQuote);
  const purchasingList = useCreatePoStore(state => state.purchasingList);
  const setPurchasingList = useCreatePoStore(state => state.setPurchasingList);
  const shortListedSearchProductsData = useSearchStore(state => state.shortListedSearchProductsData);
  const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
  const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
  const [grouped, setGrouped] = useState<GroupedData>({});
  const [viewFilter, setViewFilter] = useState<string>('newest');
  const [selectOpen, setSelectOpen] = useState<boolean>(false);
  
  // Single dropdown state
  const [dynamicDropdownOpen, setDynamicDropdownOpen] = useState<boolean>(false);
  const [dynamicDropdownValue, setDynamicDropdownValue] = useState<string>('date-created'); // Set default value
  const [lastClickedIndex, setLastClickedIndex] = useState<number | null>(null);
  const [selectedSavedSearchIdList, setSelectedSavedSearchIdList] = useState<any[]>([]);

  // Undo delete state
  const [showUndoDelete, setShowUndoDelete] = useState<boolean>(false);
  const [deletedItems, setDeletedItems] = useState<any[]>([]);
  const [undoTimer, setUndoTimer] = useState<NodeJS.Timeout | null>(null);
  const [undoCountdown, setUndoCountdown] = useState<number>(10);

  const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();
  const { mutateAsync: deleteSearchProductsMutation } = useDeleteSearchProducts();
  const {mutateAsync: cancelDraftPo} = usePostCancelDraftPo();
  const {setBomUploadID, bomUploadID, setPdfFile, setAllBoxes} = useBomPdfExtractorStore();
  const {setUploadBomInitialData} = useCreatePoStore();
  const orderManagementData = useOrderManagementStore(state => state.orderManagementData);
  const isHomePage = location.pathname === routes.homePage;
  const isQuotePage = location.pathname === routes.quotePage;
  const isPurchasingPage = location.pathname === routes.createPoPage || location.pathname === routes.orderConfirmationPage;
  const isOrderManagementPage = location.pathname === routes.orderManagementPage;

  const { selectedObject } = useGlobalSearchStore();

  const itemRefs = useRef<Record<string, HTMLElement | null>>({});
  const quoteItemRefs = useRef<Record<string, HTMLElement | null>>({});
  const orderItemRefs = useRef<Record<string, HTMLElement | null>>({});

  const scrollToItem = (itemRef: HTMLElement | null) => {
    if (!itemRef) return;
    
    const container = document.querySelector(`.${styles.savedSearchListContainer}`);
    if (!container) return;
    
    const containerRect = container.getBoundingClientRect();
    const itemRect = itemRef.getBoundingClientRect();
    
    // Calculate if item is outside visible area
    const isAboveView = itemRect.top < containerRect.top;
    const isBelowView = itemRect.bottom > containerRect.bottom;
    
    if (isAboveView || isBelowView) {
      const scrollOffset = itemRect.top - containerRect.top - 20; // 20px padding from top
      container.scrollBy({ top: scrollOffset, behavior: 'smooth' });
    }
  };

  // Dynamic options based on selected filter
  const getDynamicOptions = (filter: string=viewFilter) => {
    switch (filter) {
      case 'newest':
      case 'oldest':
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
          { value: 'date-modified', label: 'Date Modified', field: 'time_stamp' }
        ];
        
      case 'a-to-z':
      case 'z-to-a':
        return [
          { value: 'list-name', label: 'List Name', field: 'title' },
          { value: 'product-type', label: 'Product Type', field: 'product_type', disabled: true }
        ];
        
      case 'highest':
      case 'lowest':
        return [
          { value: 'item-count', label: 'Item Count', field: 'item_count' },
          { value: 'order-size', label: 'Order Size', field: 'order_size' }
        ];
        
      default:
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
          { value: 'date-modified', label: 'Date Modified', field: 'time_stamp' }
        ];
    }
  };
  
    // Add keyboard event listener for Escape key
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
          if (event.key === 'Escape') {
              setSelectedSavedSearchIdList([]);
              setLastClickedIndex(null);
              console.log('Escape pressed: Cleared all selections');
          }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
          document.removeEventListener('keydown', handleKeyDown);
      };
  }, []);

  const getItemRef = (id: string) => {
    if(location.pathname === routes.homePage) return itemRefs.current[id];
    else if(location.pathname === routes.quotePage || location.pathname === routes.createPoPage) return quoteItemRefs.current[id];
    else if(location.pathname === routes.orderManagementPage) return orderItemRefs.current[id];
    else return itemRefs.current[id];
  }

  useEffect(() => {
    if (selectedObject?.id) {
      if(selectedSavedSearchIdList.includes(selectedObject.id)) return;
      const ref = getItemRef(selectedObject.id);
      
      ref?.click(); 
      
      // Scroll after click to avoid scroll position being reset
      setTimeout(() => {
        scrollToItem(ref);
      }, 100);
    }
  }, [selectedObject]);

  const dynamicOptions = getDynamicOptions();

  const sortedData = useMemo(() => {
    setSelectedSavedSearchIdList([]);
    // setGrouped({});
    // Use savedSearchProducts instead of undefined 'data'
    let data = [];
    if(location.pathname === routes.homePage) data = savedSearchProducts
    else if(location.pathname === routes.quotePage) data = quoteList
    else if(isPurchasingPage) data = purchasingList
    else if(location.pathname === routes.orderManagementPage) data = orderManagementData
    
    // Apply filtering based on both main filter and dynamic dropdown value
    let filteredData = data;
    
    // Map dynamic dropdown value to field name
    const getFieldFromDropdownValue = (dropdownValue: string): string => {
      switch (dropdownValue) {
        case 'date-created':
          return 'created_date';
        case 'date-modified':
          return 'time_stamp';
        case 'list-name':
          return 'title';
        case 'product-type':
          return 'product_type';
        case 'item-count':
          return 'item_count';
        case 'order-size':
          return 'order_size';
        default:
          return 'created_date';
      }
    };

    const fieldToSortBy = getFieldFromDropdownValue(dynamicDropdownValue);

    // Then apply the main filter sorting
    switch (viewFilter) {
      case 'newest':
        return filterByNewest(filteredData, fieldToSortBy);
        
      case 'oldest':
        return filterByOldest(filteredData, fieldToSortBy);
        
      case 'a-to-z':
        return filterByAToZ(filteredData, fieldToSortBy);
        
      case 'z-to-a':
        return filterByZToA(filteredData, fieldToSortBy);
        
      case 'highest':
        return filterByHighest(filteredData, fieldToSortBy);
        
      case 'lowest':
        return filterByLowest(filteredData, fieldToSortBy);
        
      default:
        return filterByNewest(filteredData, fieldToSortBy);
    }
  }, [viewFilter, dynamicDropdownValue, savedSearchProducts, location.pathname, quoteList, purchasingList, orderManagementData]);

  useEffect(() => {
    console.log("sortedData ", sortedData);
    setGrouped(sortedData);
  }, [sortedData]);

  useEffect(()=>{
    if(location.pathname){
      setViewFilter('newest')
      // Set default value to first option when pathname changes
      const options = getDynamicOptions();
      setDynamicDropdownValue(options[0]?.value || '');
    }
  },[location.pathname])

const handleSaveSearchProducts = async (priceSearchData: any, saveSearchProductsMutation: any) => {
  try{
     const payload = {
         data: {
             id: priceSearchData?.id.includes(noIdGeneric) ? undefined : priceSearchData?.id,
             title: priceSearchData?.title,
             zipcode: priceSearchData?.zipcode.trim(),
             order_size: String(priceSearchData?.order_size),
             source: "search",
             products: priceSearchData?.products?.length > 0 ? priceSearchData?.products : null
         }
     }
     const response = await saveSearchProductsMutation(payload as any);

     if (response?.data?.data) {
       console.log("Pricing saved successfully");
       setSelectedProductsData([]);
       setSaveFeedbackMap({});
       setFocusSingleProduct({});
       clearLocal(localStorageKeys.instantPriceSearch);
     } else {
       console.error("Failed to save pricing");
     }
  } catch(error){
     console.error('Error saving pricing:', error);
  }
 }

  const handleViewFilterChange = (event: any) => {
    setViewFilter(event.target.value);
    // Set default value to first option when filter changes
    const newOptions = getDynamicOptions(event.target.value);
    setDynamicDropdownValue(newOptions[0]?.value || '');
  };

  const handleDynamicDropdownChange = (event: any) => {
    setDynamicDropdownValue(event.target.value);
  };

  const handleCreateNew = async () => {
    setClickedCreateNewButton(Math.random());
    setSelectedSavedSearchIdList([]);
  }

  const handleDeleteSelectedSavedSearch = () => {
    // Store the items to be deleted
    let itemsToDelete = [];
    if(isHomePage) itemsToDelete = savedSearchProducts.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else if(isQuotePage) itemsToDelete = quoteList.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else if(isPurchasingPage) itemsToDelete = purchasingList.filter((item: any) => selectedSavedSearchIdList.includes(item.id));
    else itemsToDelete = [];
    setDeletedItems(itemsToDelete);
    setSelectedSavedSearch(null);
    setShortListedSearchProductsData([]);
    setFilterShortListedSearchProductsData([]);
    setSelectedQuote(null);
    
    // Remove items from the list temporarily
    if(isHomePage){
      const updatedProducts = savedSearchProducts.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setSavedSearchProducts(updatedProducts);
    }
    else if(isQuotePage){
      const updatedProducts = quoteList.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setQuoteList(updatedProducts);
    } else if(isPurchasingPage){
      const updatedProducts = purchasingList.filter((item: any) => 
        !selectedSavedSearchIdList.includes(item.id)
      );
      setPurchasingList(updatedProducts);
    }
    
    // Clear the selection
    setSelectedSavedSearchIdList([]);
    
    // Show undo button and start countdown
    setShowUndoDelete(true);
    setUndoCountdown(10);
    
    // Start countdown timer
    const timer = setInterval(() => {
      setUndoCountdown((prev) => {
        if (prev <= 1) {
          // Time's up, actually delete the items
          handleConfirmDelete(itemsToDelete);
          setShowUndoDelete(false);
          setDeletedItems([]);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    setUndoTimer(timer);
  };

  const handleUndoDelete = () => {
    // Restore the deleted items
    restoreDeletedItems();
    
    // Clear undo state
    setShowUndoDelete(false);
    setDeletedItems([]);
    setUndoCountdown(10);
    
    // Clear the timer
    if (undoTimer) {
      clearInterval(undoTimer);
      setUndoTimer(null);
    }
  };

  const handleConfirmDelete = async (itemsToDelete: any[]) => {
    try {
      if(itemsToDelete.length === 0) {
        restoreDeletedItems();
        return;
      }
      // Call your delete API here
      const payload = {
        data: itemsToDelete.map((item: any) => item.id)
      }
      let response;
      if(isHomePage) response = await deleteSearchProductsMutation(payload as any);
      else if(isQuotePage || isPurchasingPage) response = await cancelDraftPo(payload as any);

      if (response?.data?.data) {
        console.log("Items deleted successfully");
      } else {
        console.error("Failed to delete items");
        restoreDeletedItems();
      }
      // Example API call:
      // await deleteSavedSearchAPI(itemsToDelete);
      
      // The items are already removed from the UI state
      // so no additional UI updates needed
    } catch (error) {
      console.error('Error deleting items:', error);
      // Optionally restore items on error
      restoreDeletedItems();
    }
  };

  const restoreDeletedItems = () => {
    if(isHomePage){
      const restoredProducts = [...savedSearchProducts, ...deletedItems];
      setSavedSearchProducts(restoredProducts);
    }
    else if(isQuotePage){
      const restoredProducts = [...quoteList, ...deletedItems];
      setQuoteList(restoredProducts);
    } else if(isPurchasingPage){
      const restoredProducts = [...purchasingList, ...deletedItems];
      setPurchasingList(restoredProducts);
    }
  }

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (undoTimer) {
        clearInterval(undoTimer);
      }
    };
  }, [undoTimer]);

  const handleCtrlClick = (item: any, index: number, onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void) => {
    const currentSelectedIds = [...selectedSavedSearchIdList];
    const itemId = item.id;
    let updatedIds: any[] = [];

    if (currentSelectedIds.includes(itemId)) {
      // Remove item from selection
      updatedIds = currentSelectedIds.filter(id => id !== itemId);
      setSelectedSavedSearchIdList(updatedIds);
    } else {
      // Add item to selection
      updatedIds = [...currentSelectedIds, itemId];
      setSelectedSavedSearchIdList(updatedIds);
    }
    onCtrlClickCallback(currentSelectedIds, updatedIds);
    setLastClickedIndex(index);
  };

  return (
    <div className={styles.listTab}>
      <div className={styles.topSectionBtnLeft}>
        <div className={styles.listActionContainer}>
          {!isOrderManagementPage &&
          (
          <div className={clsx(styles.createNew)} style={{display: "flex"}}>
            {(selectedSavedSearch || selectedQuote) &&
              <button className={clsx(styles.createNewBtnHover, styles.createNewBtnAnimation, styles.initialPositionForAnimation,styles.slideInAnimation5)} onClick={handleCreateNew}>
                Create New
                <div className={styles.createButtonIcon}>
                  <CreateNewPluseIconHover />
                </div>
              </button>
            }
            <button className={styles.createNewBtn}>
              Create New
              <div className={styles.createButtonIcon}>
                <CreateNewPluseIcon />
              </div>
            </button>

            
          </div>
          )}
          {selectedQuote?.bom_id &&
          <button 
              className={styles.backToReviewBtn}
              onClick={(e) => {
                setAllBoxes([]);
                setPdfFile(null);
                setBomUploadID(selectedQuote.bom_id);
                const {
                  delivery_date,
                  shipping_details,
                  order_type,
                  buyer_internal_po,
                  delivery_date_offset,
                  id
              } = selectedQuote;
                setUploadBomInitialData({
                  delivery_date,
                  shipping_details,
                  order_type,
                  buyer_internal_po,
                  delivery_date_offset,
                  id
              })
                navigatePage(location.pathname, {path: routes.bomExtractor+'?isBack=true'})
                console.log("back to review button clicked!")
              }}
              disabled={!selectedQuote || !selectedQuote?.bom_id}
            > <LeftSideArrow/>Back to Review</button>
          }
          {selectedSavedSearchIdList.length > 1 &&
            <div className={styles.deleteContainer} onClick={handleDeleteSelectedSavedSearch}>
              <span><DeleteIcon /></span> <span>x {selectedSavedSearchIdList.length}</span>
            </div>
          }
          {showUndoDelete && (
            <button className={styles.undoContainer} onClick={handleUndoDelete}>
              Undo Delete
            </button>
          )}

        </div>
      </div>
      <div className={styles.titleSection}>
        <span>
          {isQuotePage ? <span className={clsx(styles.quoting, styles.title)}>QUOTING</span> : 
           isPurchasingPage ? <span className={clsx(styles.purchasing, styles.title)}>PURCHASING</span> : 
           isOrderManagementPage ? <span className={clsx(styles.order, styles.title)}>ORDER MANAGEMENT</span> :
           <span className={clsx(styles.instantPriceSearch, styles.title)}>Instant Price Search</span>}
        </span>
      </div>
      <div className={styles.filterSection}>
        <div className={styles.filterSectionLeft}>
          <Select
            value={viewFilter}
            onChange={handleViewFilterChange}
            open={selectOpen}
            IconComponent={DropdownArrow}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            className={clsx('selectDropdown','instantPriceSearchDropdown')}
            MenuProps={
              {
                classes: {
                  paper: styles.dropDownBG
                },
              }
            }
          >
            <MenuItem value="newest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Newest</span>
              </div>
            </MenuItem>
            <MenuItem value="oldest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Oldest</span>
              </div>
            </MenuItem>
            <MenuItem value="a-to-z" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>A to Z</span>
              </div>
            </MenuItem>
            <MenuItem value="z-to-a" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Z to A</span>
              </div>
            </MenuItem>
            <MenuItem value="highest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Highest</span>
              </div>
            </MenuItem>
            <MenuItem value="lowest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Lowest</span>
              </div>
            </MenuItem>
          </Select>
        </div>
        {/* Single Dynamic Dropdown */}
        <div className={styles.filterSectionRight}>
          <Select
            value={dynamicDropdownValue}
            onChange={handleDynamicDropdownChange}
            open={dynamicDropdownOpen}
             IconComponent={DropdownArrow}
            onOpen={() => setDynamicDropdownOpen(true)}
            onClose={() => setDynamicDropdownOpen(false)}
            className={clsx('selectDropdown','instantPriceSearchDropdown')}
            MenuProps={{
              classes: {
                paper: styles.dropDownBG
              },
            }}
          >
            {dynamicOptions.map((option) => (
              <MenuItem 
                key={option.value} 
                value={option.value} 
                className={styles.menuItem}
                disabled={option.disabled}
              >
                <div className={styles.menuItemContent}>
                  <span>{option.label}</span>
                </div>
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
      <div className={styles.listSection}>
        <div className={styles.savedSearchListContainer}>
        {Object.keys(grouped).length > 0 ?
          Object.entries(grouped).map(([label, items]: any, index: number) => (
            <div key={index+label} className={styles.searchContainer}>
              <p className={styles.searchLabel}>{label}</p>
                {items.length > 0 ?
                  items.map((item: any, index: number) => {
                    return (
                      <div className={styles.searchItemContainerMain} style={{cursor: 'pointer'}} key={item.id}>
                        {isHomePage && 
                          <SavedPricingTemplate ref={el => {
                                if (el) itemRefs.current[item.id] = el;
                              }}
                              key={item.id} 
                              item={item} 
                              index={index} 
                              setLastClickedIndex={setLastClickedIndex} 
                              lastClickedIndex={lastClickedIndex} 
                              selectedSavedSearchIdList={selectedSavedSearchIdList} 
                              setSelectedSavedSearchIdList={setSelectedSavedSearchIdList} 
                              handleSaveSearchProducts={handleSaveSearchProducts} 
                              animatedItems={animatedItems}
                              handleCtrlClick={handleCtrlClick}
                              /> }
                        {(isQuotePage || isPurchasingPage) && 
                              <DraftPoTemplate ref={el => {
                                if (el) quoteItemRefs.current[item.id] = el;
                              }}
                              key={item.id} item={item} index={index} animatedItems={animatedItems}
                              selectedSavedSearchIdList={selectedSavedSearchIdList}
                              setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                              lastClickedIndex={lastClickedIndex}
                              setLastClickedIndex={setLastClickedIndex}
                              handleCtrlClick={handleCtrlClick}
                              /> }
                        {isOrderManagementPage && 
                              <OrderManagementTemplate ref={el => {
                                if (el) orderItemRefs.current[item.id] = el;
                              }}
                              key={item.id} item={item} index={index} animatedItems={animatedItems}
                              selectedSavedSearchIdList={selectedSavedSearchIdList}
                              setSelectedSavedSearchIdList={setSelectedSavedSearchIdList}
                              lastClickedIndex={lastClickedIndex}
                              setLastClickedIndex={setLastClickedIndex}
                              handleCtrlClick={handleCtrlClick}
                              /> }
                      </div>
                      
                    )
                  })
                  :
                  <div className={styles.noDataContainer}>
                    <span className={styles.noDataMessage}>
                        {location.pathname === routes.homePage ? "Your Instant Pricing activity will be saved here" : 
                        isPurchasingPage ? "Your Purchasing activity will be saved here" : 
                        isOrderManagementPage ? "Your Order Management activity will be saved here" : "Your Quote activity will be saved here"}
                    </span>
                  </div>
                }
              
            </div>
          ))
          :
          <div className={styles.noDataContainer}>
            <span className={styles.noDataMessage}>No Data Found</span>
          </div>
        }
        </div>
      </div>
    </div>
  )
}

export default ListTab
