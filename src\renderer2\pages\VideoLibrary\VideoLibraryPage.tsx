import { USE_IMAGEKIT, USE_IMGIX, useGetAllVideoLibraryTag, useGetVideoByTag, useImgixOrImageKit } from "@bryzos/giss-ui-library";
import { useEffect, useState } from "react";
import { useVideoStore } from "./VideoStore";
import SectionPills from "src/renderer2/component/SectionPills/SectionPills";
import VideoPlayer from "src/renderer2/component/videoPlayer/videoPlayer";
import ShareVideoUrl from "src/renderer2/component/ShareVideoUrl/ShareVideoUrl";
import VideoSection from "./VideoSection";
import styles from "./videoLibrary.module.scss";
import { ReactComponent as CreateNewPluseIcon } from '../../assets/New-images/New-Image-latest/createNewPluse.svg';

const VideoLibraryPage = () => {
    const getVideoByTag = useGetVideoByTag();
    const getAllVideoLibraryTag = useGetAllVideoLibraryTag();
    const imgixOrImageKit = useImgixOrImageKit();
    const [useImgixUrl, setUseImgixUrl] = useState(false);
    const [useImageKitUrl, setUseImageKitUrl] = useState(false);
    const [transformedSelectedVideo, setTransformedSelectedVideo] = useState();
    const { panelData, setPanelData, setSelectedSection, selectedSection, selectedVideo, setSelectedVideo, setShareVideoObject, shareVideoObject, setVideoSectionPills, videoSectionPills } = useVideoStore()
    const [openShare, setOpenShare] = useState(false);
    const [showVideo, setShowVideo] = useState(false);

    useEffect(() => {
        if (selectedVideo) {
            const transformedObj = {
                ...selectedVideo,
                thumbnail_s3_url_map: {
                    ...selectedVideo.thumbnail_s3_url_map
                }
            };

            if (selectedVideo?.thumbnail_s3_url_map) {
                transformedObj.thumbnail_s3_url_map.electron_player = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.electron_player);
                transformedObj.thumbnail_s3_url_map.intro_desktop = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.intro_desktop);
                transformedObj.thumbnail_s3_url_map.thumbnail_app = transformImageURL(selectedVideo?.thumbnail_s3_url_map?.thumbnail_app);
            }
            transformedObj.video_s3_url = transformVideoURL(selectedVideo?.video_s3_url, selectedSection, selectedVideo?.is_large_file);
            setShowVideo(false);
            setTransformedSelectedVideo(transformedObj);
        }
    }, [selectedVideo]);

    useEffect(() => {
        if (shareVideoObject) {
            setOpenShare(true);
        }
    }, [shareVideoObject]);

    useEffect(() => {
        getAllVideos();
    }, [])

    const transformImageURL = (rawUrl: string) => {
        if (!rawUrl) return "";
        const s3Path = rawUrl.split(".com")[1];
        return useImgixUrl ? import.meta.env.VITE_IMGIX_PREFIX + s3Path + import.meta.env.VITE_IMGIX_SUFFIX : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3Path;
    };

    const transformVideoURL = (rawUrl: string, section, isLarge) => {
        if (!rawUrl) return "";
        const s3Path = rawUrl.split(".com")[1];
        let url = useImageKitUrl
            ? import.meta.env.VITE_IMAGEKIT_PREFIX +
            s3Path
            : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3Path;
        url += '?tag=' + section;
        if (isLarge && useImageKitUrl) {
            url += '&tr=orig';
        }
        return url;
    }

    const getAllVideos = async () => {
        try {
            const response = (await imgixOrImageKit.mutateAsync())?.data?.data;
            if (response) {
                const imgixObj = response.find(res => res.config_key === USE_IMGIX);
                if (imgixObj) {
                    setUseImgixUrl(imgixObj.config_value);
                }
                const imgeKitObj = response.find(res => res.config_key === USE_IMAGEKIT);
                if (imgeKitObj) {
                    setUseImageKitUrl(imgeKitObj.config_value);
                }
            }
            const libraryTagsList = (await getAllVideoLibraryTag.mutateAsync()).data?.data;
            if (libraryTagsList) {
                const tagList = libraryTagsList.reduce((tags, curr_tag) => {
                    if (curr_tag.show_on_app) tags.push(curr_tag.query_param);
                    return tags;
                }, []);
                getVideoByTag.mutateAsync(tagList).then((data) => {
                    setPanelData(data?.data?.data);
                    const videoSectionPills = Object.keys(data?.data?.data).map((key) => {
                        return {
                            label: key,
                            id: key,
                            disabled: false
                        };
                    });
                    setVideoSectionPills(videoSectionPills);
                })
            }
        }
        catch (err) {
            console.log(err);
        }
    }

    const openShareVideoPopup = () => {
        setShareVideoObject({ video_id: transformedSelectedVideo?.id, share_video_url: transformedSelectedVideo?.share_video_url });
    };

    const shareVideoPopupClose = () => {
        setOpenShare(false);
        setShareVideoObject(null);
    };


    return (
        <div className={styles.videoPlayerMain}>
            <div className={styles.videoPlayerTitleMain}>
                <div className={styles.videoPlayerTitle}>THE BRYZOS VAULT</div>
                <p>Go beyond the surface - get the all-access pass to the people, the stories, and the drive that power Bryzos. From the docuseries Cracking the Code to the podcast Irregardless, go behind the scenes with the team that’s reshaping metal procurement and their guests sharing their own experiences. Real conversations. Real decisions. Real grit. See why Bryzos was built for you — and how it’s changing the game for good.</p>
            </div>
            {videoSectionPills &&
                <SectionPills
                    items={videoSectionPills}
                    activeId={selectedSection}
                    onChange={(value) => {
                        setSelectedSection(value);
                        setSelectedVideo(panelData[value][0]);
                    }}
                />
            }
            <div className={styles.videoMainContainer}>
            {(transformedSelectedVideo && transformedSelectedVideo?.video_s3_url) ?
                <>
                    <h2 className={styles.videoPlayerMainTitle}>{transformedSelectedVideo?.title}</h2>
                    <p className={styles.videoPlayerDescription}>{transformedSelectedVideo?.description}&nbsp;</p>
                    
                    <VideoPlayer
                        url={transformedSelectedVideo?.video_s3_url}
                        width={"100%"}
                        height={"300px"}
                        autoPlay={!transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player}
                        playNextVideo={true}//{playNextVideo}
                        disableNextVideoBtn={true}//{disableNextVideoBtn}
                        captionUrl={transformedSelectedVideo?.subtitle_s3_url}
                        pauseImageUrl={transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player}
                    />
                    <p className={styles.videoPlayerDescriptionBottom}>{transformedSelectedVideo?.caption}&nbsp;</p>
                    <div className={styles.videoPlayerShareButton}>
                        <button className={styles.addWatchlistButton}>Add to My Watchlist <CreateNewPluseIcon /></button>
                        { transformedSelectedVideo?.share_video_url &&
                            <button className={styles.shareVideoButton} onClick={openShareVideoPopup}>Share Video</button>
                        }
                    </div>
                </>:
                <>
                    (transformedSelectedVideo && transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player) && 
                    <img src={transformedSelectedVideo?.thumbnail_s3_url_map?.electron_player} height={"300px"} width={"100%"} alt={transformedSelectedVideo?.caption}></img>
                </>
            }
            
            {
                selectedSection && transformedSelectedVideo &&
                <VideoSection
                    section={selectedSection}
                    sectionList={panelData[selectedSection]}
                    activeId={selectedVideo?.id}
                    onItemClick={
                        (videoObject, index) => {
                            setSelectedVideo(videoObject)
                        }
                    }
                    onPlayClick={
                        (videoObject, index) => {
                            setSelectedVideo(videoObject);
                        }
                    } />
            }
            <ShareVideoUrl openShare={openShare} shareVideoPopupClose={shareVideoPopupClose} videoData={transformedSelectedVideo} />
            </div>
        </div>
    );
};

export default VideoLibraryPage;