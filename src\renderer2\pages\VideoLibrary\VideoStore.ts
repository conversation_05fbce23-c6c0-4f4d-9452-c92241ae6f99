import { SectionPillItem } from 'src/renderer2/component/SectionPills/SectionPills';
import { create } from 'zustand';


const defaultStore = {
    selectedVideo: {},
    selectedSection: '',
    panelData:{},
    thumnailClicked: false,
    videoSectionPills:[],
    shareVideoObject:null
};

interface VideoStore {
    panelData: any;
    thumnailClicked: boolean;
    selectedVideo: any;
    selectedSection: string;
    videoSectionPills:SectionPillItem[];
    shareVideoObject: any;
    setShareVideoObject: (v: any) => void;
    setVideoSectionPills: (v: SectionPillItem[]) => void;
    setSelectedSection: (v: string) => void;
    setSelectedVideo: (v: any) => void;
    setThumnailClicked: (v: boolean) => void;
    setPanelData: (panelData: any) => void;
}

export const useVideoStore = create<VideoStore>((set) => ({ 
    ...defaultStore,
    setShareVideoObject: (v) => set(state => ({ shareVideoObject: typeof v === 'function' ? v(state.shareVideoObject) : v })),
    setVideoSectionPills: (v) => set(state => ({ videoSectionPills: typeof v === 'function' ? v(state.videoSectionPills) : v })),
    setSelectedSection: (v) => set(state => ({ selectedSection: typeof v === 'function' ? v(state.selectedSection) : v })),
    setSelectedVideo: (v) => set(state => ({ selectedVideo: typeof v === 'function' ? v(state.selectedVideo) : v })),
    setThumnailClicked: (v) => set(state => ({ thumnailClicked: typeof v === 'function' ? v(state.thumnailClicked) : v })),
    setPanelData: (v) => set(state => ({ panelData: typeof v === 'function' ? v(state.panelData) : v })),
}));