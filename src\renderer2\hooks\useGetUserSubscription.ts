// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { useGlobalStore, userRole, useSubscriptionStore } from "@bryzos/giss-ui-library";

const useGetUserSubscription = () => {
  const { setShowLoader, subscriptionStatus, userData } = useGlobalStore();
  const { setUserSubscription } = useSubscriptionStore();
  const setGlobalUserSubscription = useGlobalStore((state) => state.setUserSubscription);

  return useQuery(
    [reactQueryKeys.getUserSubscription, subscriptionStatus, userData?.data?.email],
    async () => {
      try {
        const response = axios.get(import.meta.env.VITE_API_SUBSCRIPTION_SERVICE + '/subscription-service-api/api/v1/subscriptions');
        const responseData = await response;
        console.log("responseData >>>>>", responseData)
        if (responseData.data && responseData.data.data) {
            const userSubscription = responseData.data.data;
            console.log("userSubscription >>>>>", userSubscription)
            setUserSubscription(userSubscription);
            setGlobalUserSubscription(userSubscription);
            return userSubscription;
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      } 
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      staleTime:0,
      cacheTime:0,
      enabled: (!!userData?.data && userData?.data?.type === userRole.buyerUser)
    }
  );
};

export default useGetUserSubscription;
