import React, { useRef } from 'react';
import './OrientationSlider.scss';

const marks = [-180, -90, 0, 90, 180];

const OrientationSlider = ({
  handlePageRotation,
  orientation,
  setOrientation
}: {
  handlePageRotation: (orientation: number) => void;
  orientation: number;
  setOrientation: (orientation: number) => void;
}) => {
  const trackRef = useRef<HTMLDivElement>(null);

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setOrientation(value);
    const rotationValue = (value + 360) % 360;
    handlePageRotation(rotationValue);
  };

  const handleTrackClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!trackRef.current) return;

    const rect = trackRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickPercent = clickX / rect.width;

    const value = Math.round(clickPercent * 360 - 180);
    const closestMark = marks.reduce((prev, curr) =>
      Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev
    );

    if (closestMark !== orientation) {
      setOrientation(closestMark);
      const rotationValue = (closestMark + 360) % 360;
      handlePageRotation(rotationValue);
    }

    e.stopPropagation();
  };

  function getMarkPosition(mark: number) {
      switch(mark){
        case -180:
          return 7;
        case -90:
          return 29;
        case 0:
          return 50;
        case 90:
          return 71;
        case 180:
          return 93;
      }
  }

  return (
    <div className="orientation-slider-container">
      <span className="orientation-label">ORIENTATION</span>

      <div className="slider-wrapper">
        <div className="slider-track" ref={trackRef} onClick={handleTrackClick} 
            onMouseDown={(e) => e.stopPropagation()}>
          <input
            type="range"
            min="-180"
            max="180"
            step="1"
            value={orientation}
            onChange={handleSliderChange}
            className="slider-input"
          />

          <div className="marks-container">
            {marks.map((mark) => (
              <div
                key={mark}
                className="mark"
                style={{ left: `${getMarkPosition(mark)}%` }}
              >
                <div className="mark-line" />
              </div>
            ))}
          </div>
        </div>

        <div className="value-display">
          <span className="value-number">{orientation}</span>
        </div>

        <span className="value-unit">DEG</span>
      </div>
    </div>
  );
};

export default OrientationSlider;
