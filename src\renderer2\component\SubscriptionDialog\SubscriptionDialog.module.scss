.subscriptionDialog {

  width: 100%;
  height: 100%;
}

.dialogContent.dialogContent {
  background-color: transparent;
  border-radius: 0px;
  padding: 0px;
  min-width: 100%;
  // max-width: 600px;
  position: relative;
  box-shadow: unset;
  z-index: 101;
  display: flex;
  max-width: 100%;
  width: 100%;
  max-height: 100%;
}

.closeIcon {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1;
  color: #666;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #333;
  }

  svg {
    width: 20px;
    height: 20px;
  }
}

.dialogTitle {
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

.dialogText {
  color: #666;
  line-height: 1.5;
  text-align: center;
}

.editPaymentContainer {
  padding: 7px 0px 0px 0px;
}

.subscriptionSetup {
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
  width: 100%;

  &.editLicenseContainer {
    padding: 19px 0px 0px 0px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 16px;

    .stepTitle {
      font-family: Syncopate;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.64px;
      text-align: left;
      color: #fff;
    }

    .licenseCount {
      font-family: Syncopate;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.64px;
      text-align: right;
      color: #32ccff;

      span {
        font-weight: normal;
      }
    }
  }

  .content {
    display: grid;
    grid-template-columns: 25% auto 25%;
    gap: 16px;

    .panel {
      padding: 16px;
      border-radius: 13px;
      background-color: #222329;
      min-height: 212px;
      display: flex;
      flex-direction: column;

      .panelContent1 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        column-gap: 20px;

        .panelTitle {
          font-family: Inter;
          font-size: 12px;
          font-weight: normal;
          line-height: 1.4;
          letter-spacing: normal;
          text-align: left;
          color: #fff;
          flex: 0 80%;
        }

        .quantityInput {
          height: 64px;
          flex: 0 20%;

          input {
            width: 100%;
            height: 100%;
            padding: 16px;
            border-radius: 13px;
            background-color: rgba(255, 255, 255, 0.04);
            border: none;
            border-radius: 8px;
            font-family: Teko;
            font-size: 48px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: right;
            color: #fff;
            padding: 0 16px;

            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px #007bff;
            }
          }
        }

        .priceInfo {
          font-family: Inter;
          font-size: 12px;
          font-weight: normal;
          line-height: 1.3;
          letter-spacing: normal;
          text-align: left;
          color: #9b9eac;
        }
      }


      .paymentMethod {
        margin-bottom: 20px;

        .paymentSelect {
          width: 100%;
          height: 50px;
          background: #404040;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          color: white;
          padding: 0 16px;
          cursor: pointer;

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px #007bff;
          }

          option {
            background: #404040;
            color: white;
          }
        }
      }

      .cardElement {
        margin-top: 20px;
        padding: 16px;
        background: #404040;
        border-radius: 8px;
        border: 1px solid #555;
      }

      .orderSummaryHeader {
        display: grid;
        grid-template-columns: 40% 18% 24% 18%;
        font-family: Syncopate;
        font-size: 10px;
        font-weight: normal;
        line-height: 1.2;
        letter-spacing: normal;
        text-align: left;
        color: #c3c4ca;
        padding: 0px 16px;

        div {
          &:nth-child(2) {
            text-align: right;
          }

          &:nth-child(4) {
            text-align: right;
          }
        }
      }

      .orderSummary {
        margin: 8px 0px 12px 0px;
        padding: 12px 16px;
        border-radius: 8px;
        background-color: #191a20;
        display: flex;
        flex-direction: column;
        row-gap: 8px;

        .summaryRow {
          display: grid;
          grid-template-columns: 40% 18% 24% 18%;
          align-items: center;
          font-family: Inter;
          font-size: 12px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.2;
          letter-spacing: normal;
          text-align: left;
          color: #c3c4ca;

          span {
            &:nth-child(2) {
              text-align: right;
            }

            &:nth-child(3) {
              text-align: right;
              padding: 0px 12px;
            }

            &:nth-child(4) {
              text-align: right;
            }
          }
        }
      }

      .summaryTotalRow {
        margin-bottom: 20px;
        padding: 0px 16px;
        display: grid;
        grid-template-columns:40% 18% 24% 18%;
        align-items: center;
        font-family: Inter;
        font-size: 14px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.56px;
        text-align: left;
        color: #fff;

        span {
          &:nth-child(2) {
            text-align: right;
          }

          &:nth-child(3) {
            text-align: right;
             padding: 0px 12px;
          }

          &:nth-child(4) {
            text-align: right;
          }
        }
      }

      .disclaimer {
        font-family: Inter;
        font-size: 10px;
        font-weight: 200;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #c3c4ca;
        margin-bottom: 12px;

        .link {
          color: #ffa341;
          cursor: pointer;
          text-decoration: none;
        }
      }
    }
  }
}

.editLicenseContainer {
  .content {
    display: flex;
    flex-direction: column;
    row-gap: 16px;

    .panel {
      min-height: unset;
       background-color: rgba(255, 255, 255, 0.04);
    }
  }
}

.paymentDetailsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;

  .paymentDetails {
    display: flex;
    column-gap: 16px;
    row-gap: 16px;

    .formField {
      flex: 0 50%;
      width: 100%;
      height: 36px;
      font-size: 14px;
      padding: 0 12px;
      border-radius: 11px;

      &::placeholder {
        text-transform: uppercase;
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 0.56px;
        text-align: left;
        color: #71737f;
      }
    }
  }

  .stripeElement {
    width: 100%;
    height: 36px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 8px 12px;
    border-radius: 11px;
    background-color: rgba(255, 255, 255, 0.04);
    flex: 1;

    &>div {
      width: 100%;
    }

    /* Extra styling to ensure the iframe is visible */
    iframe {
      opacity: 1 !important;
      height: 24px !important;
      min-height: 24px !important;
      width: 100% !important;
    }
  }
}

.editPaymentBtnMain {
  width: 498px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-end;
}

.editPaymentBtn {
  width: 199px;
  height: 36px;
  flex-grow: 0;
  padding: 8px 16px;
  border-radius: 500px;
  background-color: #2abcfb;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #0f0f14;
  margin-left: auto;
}

.purchaseButton {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px 12px;
  border-radius: 10px;
  border: solid 1px transparent;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.8);
  background-image: linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
  color: #fff;
  font-family: Syncopate;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.56px;
  text-align: center;
  margin-top: auto;
  text-transform: uppercase;
  &[disabled] {
    cursor: not-allowed;
    border: solid 1px rgba(255, 255, 255, 0.16);
    background-color: #222329;
    background-image: none;
    box-shadow: none;
    color: #71737f;

  }
}

.accountTypeDropdown {
  flex: 0 50%;
}


.paymentMethodSelect.paymentMethodSelect {
  width: 100%;

  :global(.MuiSelect-select) {
    width: 100%;
    height: 36px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0 12px;
    border-radius: 11px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.56px;
    text-align: left;
    color: #fff;
  }

  :global(.Mui-disabled) {
    opacity: 0.3;
    cursor: not-allowed;
  }

  &.selectState {
    :global(.MuiSelect-select) {
      height: 36px;
      font-size: 14px;
    }
  }

  :global(.MuiSelect-icon) {
    width: 16px;
    height: 16px;

    path {
      fill: #71737f;
      stroke-opacity: unset;
    }

    top: unset;
    right:12px;
  }


  :global(fieldset) {
    border-color: transparent !important;
  }
}

.Dropdownpaper.Dropdownpaper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  padding: 4px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #9c9da5;
  padding: 0px;
  margin-top: 1px;

  .muiMenuList {
    padding: 4px;

    li {
      padding: 6px 16px;
      border-radius: 6px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #191a20;
      background-color: transparent;

      &:hover {
        background-color: #e0e0e0;
        color: #191a20;
        font-weight: bold;
      }
    }
  }


}

.cardInfo{
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
  margin-bottom: 7px;
}


.editPaymentNote {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #9b9eac;
  margin-bottom: 16px;
}

 .editLicenseForm{
       .cardInfo, .editPaymentNote {
        font-size: 12px;
       }
  }

  .subscribeTabContainer{
    display: flex;
    gap: 16px;
    width: 100%;

    .subscribeLeftContainer{
     display: flex;
     flex-direction: column;
     flex: 1;
    }
  }
  
  .headerMain {
    width: 100%;
    height: 68px;
    padding: 16px;
    background-color: #32ff6c;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .stepTitle {
      font-family: Syncopate;
      font-size: 23px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.92px;
      text-align: left;
      color: #0f0f14;
      display: flex;
      align-items: center;
    }

    .closeBtn {
      width: 118px;
      height: 36px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;
      padding: 8px 24px;
      border-radius: 500px;
      background-color: #00c738;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
      cursor: pointer;

      svg {
        width: 24px;
        height: 24px;

        path {
          fill: #0f0f14;
        }
      }
    }
  }

  .subscriptionSetupContainer{
    display: flex;
    column-gap: 16px;
    width: 100%;

    .LeftPanelContainer{
      flex: 1;
      display: flex;
    }
    
  }

  .subscribeLeftContainer{
    flex: 1;
    display: flex;
    flex-direction: column;
  }