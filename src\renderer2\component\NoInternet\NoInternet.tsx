import React, { useEffect, useState } from "react";
import { ReactComponent as NoInternetIcon } from '../../assets/images/No_Internet_Connection.svg';
import styles from './NoInternet.module.scss';
import { getChannelWindow, reconnectToSocket, useGlobalStore, useSellerOrderStore } from "@bryzos/giss-ui-library";
import clsx from "clsx";
import { ReactComponent as Logo } from '../../../../public/asset/new-bryzos-logo-with-text.svg';

const NoInternet = () => {
    const { noInternetAccessibility, apiFailureDueToNoInternet, onlineStatus, setOnlineStatus }: any = useGlobalStore();
    const {socketDisconnectAfterMaxRetries, setSocketDisconnectAfterMaxRetries}: any = useSellerOrderStore();
    const channelWindow:any =  getChannelWindow();

    useEffect(() => {
        setOnlineStatus(navigator.onLine); 

        const handleOnline = () => {
          setOnlineStatus(true);
        };
    
        const handleOffline = () => {
          setOnlineStatus(false);
        };
    
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
    
        return () => {
          window.removeEventListener('online', handleOnline);
          window.removeEventListener('offline', handleOffline);
        };
      }, []);
    
    if(!(apiFailureDueToNoInternet || noInternetAccessibility || !onlineStatus)) return<></>

    const minimizeBtnClick = () => {
        if(channelWindow?.minimize){
            window.electron.send({ channel: channelWindow.minimize })
        }
    }

    const closeBtnClick = () => {
        if(channelWindow?.close){
            window.electron.send({ channel: channelWindow.close })
        }
    }

    const retryConnection = () => {
        reconnectToSocket();
    }

    const refreshApp = () => {
        if(channelWindow?.refreshApp)
            window.electron.send({ channel: channelWindow.refreshApp });
    }

    return (
        <div className={clsx(styles.container, styles.flex_center)}>
            <div className={styles.noInternetBody}>
                <div className={clsx(styles.main_content, styles.flex_center)}>
                    <div className={styles.bryzosName}>BRYZOS</div>
                    <div className={clsx(styles.flex_center, styles.noInternetIcon)}>
                        <NoInternetIcon/>
                    </div>
                    <p className={styles.header}>NO INTERNET CONNECTION</p>
                    <p className={styles.content}>Please check your internet connection</p>
                    {(apiFailureDueToNoInternet || !onlineStatus) ? (
                        <button className={styles.try_again_btn} onClick={refreshApp}>REFRESH APP</button>
                    ) : (socketDisconnectAfterMaxRetries) && (
                        <button className={styles.try_again_btn} onClick={retryConnection}>RECONNECT</button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NoInternet;