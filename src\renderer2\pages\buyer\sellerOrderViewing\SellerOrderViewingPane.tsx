import React, { useEffect } from 'react'
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import styles from './SellerOrderViewingPane.module.scss';
import SellerOrderViewingActionWindow from './SellerOrderViewingActionWindow';
import { useSellerOrderStore } from '@bryzos/giss-ui-library';
import AcceptOrder from '../../seller/acceptOrder';

const SellerOrderViewingPane = ({containerRef}) => {
  const { setLoadComponent, setProps } = useRightWindowStore();
  const { orderToBeShownInOrderAccept } = useSellerOrderStore();

  useEffect(() => {
    setLoadComponent(<SellerOrderViewingActionWindow />);
    return () => {
      setLoadComponent(null);
    }
  }, [location.pathname]);

  useEffect(() => {
    if(!(Object.keys(orderToBeShownInOrderAccept).length > 0)){
      setLoadComponent(<SellerOrderViewingActionWindow />);
    }
  }, [orderToBeShownInOrderAccept])



  return (
    <>
      {
        Object.keys(orderToBeShownInOrderAccept).length > 0 ? (
            <AcceptOrder key={orderToBeShownInOrderAccept.id} containerRef={containerRef}/>
        ) : (
          <div className={styles.viewingPane}>
            <div className={styles.placeholderContent}>
              <p className={styles.placeholderText}>
                This is the viewing pane.
              </p>
              <p className={styles.placeholderText}>
                Select from the list on the left to
              </p>
              <p className={styles.placeholderText}>
                review an order.
              </p>
            </div>
          </div>

        )
      }
    </>
  )
}

export default SellerOrderViewingPane
