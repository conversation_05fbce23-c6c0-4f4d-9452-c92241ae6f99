
// @ts-nocheck
import { useLocation, useNavigate } from 'react-router';
import styles from './onboardingTnC.module.scss'
import clsx from 'clsx';
import { useEffect, useState, useRef } from 'react';
import { fileType, routes } from '../../common';
import OnboardingFooter from './onboardingFooter';
import { commomKeys, useGlobalStore, userRole, getChannelWindow, downloadFilesUsingFetch } from '@bryzos/giss-ui-library';
import { Dialog } from '@mui/material';
import axios from 'axios';
import { Auth } from 'aws-amplify';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import SearchHeader from '../SearchHeader';
import { ReactComponent as BackArrow } from '../../assets/New-images/Create-Account/arrow-left.svg';
import { ReactComponent as ScrollArrow } from '../../assets/New-images/New-Image-latest/scroll-arrow.svg';

function OnboardingTnc() {
    const channelWindow =  getChannelWindow();
    const navigate = useNavigate();
    const location = useLocation();
    const [tncCheckbox, setTncCheckBox] = useState(false);
    const [tncCheckboxDisable, setTncCheckBoxDisable] = useState(false);
    const [tncData, setTncData] = useState({});
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [showScrollIndicator, setShowScrollIndicator] = useState(true);
    const [hasEverReachedBottom, setHasEverReachedBottom] = useState(false);
    const [scrollProgress, setScrollProgress] = useState(0);
    const {setShowLoader, setSignupUser, systemVersion, appVersion} = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const {data, isTncChecked} = location.state;
    // const isTncChecked = false;
    // const data = {
    //     userType: 'BUYER',
    //     companyName: 'Test Company',
    //     firstName: 'Test',
    //     lastName: 'User',
    //     emailAddress: '<EMAIL>',
    //     password: 'Test@123'
    // }
    const scrollContainerRef = useRef(null);
  
    useEffect(() => {
        const handleTncData = async () =>{
            try{
                const responseData = await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/homepage');
                responseData.data.ref_bryzos_terms_conditions.forEach(termsAndCondition => {
                    if(data.userType === termsAndCondition.type){
                        setTncData(termsAndCondition)
                    }
                })
            }catch (error) {
                console.error('onboarding tnc error',error)
            }
        }
        setShowLoader(true)
        if(isTncChecked){
            setTncCheckBox(true);
            setTncCheckBoxDisable(true);
            setHasEverReachedBottom(true);
        }
        handleTncData();
    },[])
    const handleOnboardingSubmit = async () => {
        try{
            setTncCheckBoxDisable(false)
            if(data){
                let deviceId = null;
                //sign-up
                const user = await Auth.signUp({
                    username: data.emailAddress,
                    password: data.password,
                    email: data.emailAddress,
                    attributes: {
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'firstname']: data.firstName,
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'lastname']: data.lastName,
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'type']: data.userType
                    }
                });
                const payloadData = {
                    "data": {
                        "username": data.emailAddress
                    }
                };
                const userData = {
                    email: data.emailAddress,
                    password: data.password
                }
                if (channelWindow?.getDeviceId) {
                    deviceId = window.electron.sendSync({ channel: channelWindow.getDeviceId });
                }
                const payload = {
                    data: {
                        first_name: data.firstName,
                        last_name: data.lastName,
                        company_name: data.companyName,
                        client_company: data.companyName,
                        email_id: data.emailAddress,
                        user_type: data.userType,
                        cognito_user_name: user.userSub,
                        bryzos_terms_condtion_id: tncData.id,
                        accepted_terms_and_condition: tncData.terms_conditions_version,
                        last_login_app_version: appVersion,
                        os_version: systemVersion,
                        device_id: deviceId,
                        ui_version: import.meta.env.VITE_RENDERER_DEPLOY_VERSION,
                        zip_code: data.zipCode,
                    }
                }
                const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/signup', payload);
                if(responseData.data.data.error_message){
                    navigate(routes.onboardingDetails, { state: {data, errorMessage: responseData.data.data.error_message, tncCheckbox, tncData}})
                }else{
                    const isUserApproved = responseData.data.data.is_approved;
                    if(isUserApproved){ 
                        setSignupUser(userData); 
                        navigate(routes.loginPage)
                    }else{
                        navigate(routes.onboardingThankYou);
                    }
                }
            }else{
                navigate(routes.onboardingDetails);
            }
        }catch (error) {
            console.error('onBoarding error',error)
            setTncCheckBoxDisable(true)
        }

    }
    const [tnc, setTnc] = useState('');
    const FetchHtml = async () => {
        const response = await fetch(tncData.cloudfront_url+"?ver="+new Date().getTime());
        return await response.text(); // Returns it as Promise
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
            setShowLoader(false)
        });
    };
    useEffect(() => {
        if(Object.keys(tncData).length !== 0){
            SetHtml(true)
        }
    },[tncData]);

    // Handle scroll detection
    const handleScroll = () => {
        if (scrollContainerRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold
            
            if (isAtBottom && !hasEverReachedBottom) {
                setHasEverReachedBottom(true);
            }
            
            // Calculate scroll progress (0 to 1)
            const progress = scrollTop / (scrollHeight - clientHeight);
            setScrollProgress(Math.min(progress, 1));
            
            // Always show scroll indicator when there's content below
            setShowScrollIndicator(!isAtBottom);
        }
    };

    // Scroll to bottom functionality
    const scrollToBottom = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
        }
    };

    const downloadReports = () => {
        const url = data.userType === userRole.sellerUser ? import.meta.env.VITE_FILE_URL_DOWNLOAD_SELLER_TNC_PDF : import.meta.env.VITE_FILE_URL_DOWNLOAD_BUYER_TNC_PDF;
        const showError = downloadFilesUsingFetch (`${url}?ver=${new Date().getTime()}`, 'Tnc', fileType.pdf)
        showError.then(res => {
            if (res) {
                setOpenErrorDialog(false);
            } else {
                setOpenErrorDialog(true);
            }
        })

    }

    return (
        <div className={styles.tncBox}>
            <div className={styles.title}>Terms and Conditions</div>
            <div className={styles.tnCInnerContent}>
            <div className={clsx(styles.tncBoxContent, tncCheckbox && styles.onboardingTnCBody)}>
                <div className={styles.tnCPage} ref={scrollContainerRef} onScroll={handleScroll}>
                    <div className={styles.tncScrollClass}>
                        <h2 className={styles.TermsofUseV1}>Bryzos Instant Pricing Desktop Widget
                            Terms of Use ({tncData?.terms_conditions_version?.toUpperCase()})</h2>
                        {/* <p>Please read these Bryzos Instant Pricing Desktop Widget Terms of Use (these "Terms") carefully as they govern your use of (which includes access to) Bryzos' Instant Pricing Desktop Widget (the "Widget").</p> */}
                        {/* <p>Use of the Widget may be subject to additional terms and conditions presented by Bryzos, which are hereby incorporated by this reference into these Terms.</p> */}
                        <div className='tncOnboarding'>
                            <div dangerouslySetInnerHTML={{ __html: tnc }}></div>
                        </div>
                        
                    </div>
                </div>

            </div>
             {/* Scroll to Bottom Indicator */}
                {showScrollIndicator && (
                    <div 
                        className={styles.scrollToBottomIndicator} 
                        onClick={scrollToBottom}
                        style={{
                            transform: `translateY(${scrollProgress * 100}%)`
                        }}
                    >
                        <div className={styles.scrollArrows}>
                            <ScrollArrow/>
                        </div>
                        <span className={styles.scrollText}>Scroll Down To The Bottom</span>
                        <div className={styles.scrollArrows}>
                            <ScrollArrow/>
                        </div>
                    </div>
                    )}
            </div>

             <div className={clsx(styles.checkingThisbox, !hasEverReachedBottom && styles.checkingThisboxShow)}>
                    <label className={styles.containerChk}>
                        <input 
                            type='checkbox' 
                            onChange={(e) => {
                                if (hasEverReachedBottom) {
                                    setTncCheckBox(e.target.checked); 
                                    setTncCheckBoxDisable(e.target.checked);
                                }
                            }} 
                            checked={tncCheckbox}
                            disabled={!hasEverReachedBottom}
                            onKeyDown={(e)=>{
                                if(e.key === 'Enter' && hasEverReachedBottom){
                                    setTncCheckBoxDisable(!tncCheckboxDisable)
                                    setTncCheckBox(!tncCheckbox)
                                }
                            }}
                         />
                        <span className={styles.checkmark} />
                        
                    </label>
                    <span className={styles.lblChk}>
                       By checking this box I am confirming on behalf of myself, and the company which I represent that I have read, understand and agree to the above terms and conditions.                    </span>
                </div>

            {/* <div className={styles.btnSectionTnC}>
            <div className={styles.onBoradingDownloadTnC} onClick={() => { downloadReports() }}>
                    Click here to download T&C
                </div>
            </div> */}

            <div className={styles.btnFooterTnc}>
                <div className={styles.btnBack}>
                    <button onClick={() => { navigate(routes.onboardingDetails, { state: { data, tncCheckbox, tncData } }) }}><BackArrow/>Back</button>
                </div>
                <div className={styles.alreadyAccountLogin}>Already have an account? <span onClick={() => navigate(routes.loginPage)}>Login</span></div>
                <div><button className={styles.nextTncBtn} disabled={!tncCheckboxDisable}  onClick={handleOnboardingSubmit}><span>Next</span></button></div>

            </div>

            {/* <OnboardingFooter /> */}
            <>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p>No data found. Please try again in sometime</p>
                    <button className={styles.submitBtn} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
                </Dialog>
            </>
        </div>
    );
}
export default OnboardingTnc;
