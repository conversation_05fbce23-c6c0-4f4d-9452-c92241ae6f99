import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import {
  useInAppNotificationStore,
  formatRelativeLabel,
} from "./InAppNotificationStore";
import usePostClearInAppNotifications from "src/renderer2/hooks/usePostClearInAppNotifications";
import usePostMarkReadInAppNotifications from "src/renderer2/hooks/usePostMarkReadInAppNotifications";
import "./InAppNotificationList.scss";
import { createActionUrl } from "src/renderer2/helper";

type Props = {
  onClose?: () => void;
};

export default function InAppNotificationList({ onClose }: Props) {
  const navigate = useNavigate();
  const {
    loading,
    error,
    getUnreadList,
    getReadList,
    clearAll,
    clearByIds,
    onPanelOpen,
    order,
    setNavigationURL,
    onPanelClose,
  } = useInAppNotificationStore((s) => ({
    loading: s.loading,
    setNavigationURL: s.setNavigationURL,
    error: s.error,
    unreadList: s.unreadList,
    readList: s.readList,
    unreadCount: s.unreadCount,
    getUnreadList: s.getUnreadList,
    getReadList: s.getReadList,
    clearAll: s.clearAll,
    clearByIds: s.clearByIds,
    onPanelOpen: s.onPanelOpen,
    onPanelClose: s.onPanelClose,
    order: s.order,
  }));

  const { mutateAsync: clearHook } = usePostClearInAppNotifications();
  const { mutateAsync: markHook } = usePostMarkReadInAppNotifications();

  const unreadList = getUnreadList();
  const readList = getReadList();

  useEffect(() => {
    onPanelOpen();
    
    return () => {
      const ids = onPanelClose();
      if (ids.length > 0) {
        markHook(ids).catch((e) => console.error(e));
      }
    };
  }, []);

  const handleRowClick = (id: string, route: string | null) => {
    if(route){
      const url = createActionUrl(route, id);
      setNavigationURL(url);      
    }
  };

  const handleRowClear = async (id: string) => {
    try {
      await clearHook([id]);
      clearByIds([id]);
    } catch (e) {
      console.error(e);
    }
  };

  const clearAllNotifications = async () => {
    try {
      await clearHook([]);
      clearAll();
    } catch (e) {
      console.error(e);
    }
  };

  if (loading) {
    return (
      <div className="notification-loader">
        <div className="loader">Loading...</div>
      </div>
    );
  }

  if (!unreadList.length && !readList.length) {
    return (
      <div className="notification-empty">
        <p>You’re all caught up.</p>
      </div>
    );
  }

  return (
    <div className="notification-container">
      {error && <div className="notification-error">{error}</div>}

      {/* Unread */}
      {unreadList.length > 0 && (
        <div className="notification-section">
          {unreadList.map((n) => (
            <div className="notification-item-container">
              <div className="dot"></div>
              <div key={n.id} className="notification-item unread">
                
                <div className="notification-header">
                    <span className="time">
                      {formatRelativeLabel(n.sentEpoch)}
                    </span>
                  </div>
                <div className="notification-content" onClick={() => handleRowClick(n.id, n.notification_route)}>
                  <p className="message"><span className="title">{n.notification_title}</span> {n.message}</p>
                  <div className="clear-btn" onClick={() => handleRowClear(n.id)}>
                    <CloseIcon />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Clear All */}
      {(unreadList.length > 0 || readList.length > 0) && (
        <div className="clear-all">
          <button onClick={clearAllNotifications}>Clear All</button>
        </div>
      )}

      {/* Read */}
      {readList.length > 0 && (
        <div className="notification-section read">
          {readList.map((n) => (
            <div className="notification-item-container">
              <div className="dot dotUnread"></div>
              <div key={n.id} className="notification-item read">
                <div className="notification-header">
                    
                    <span className="time">
                      {formatRelativeLabel(n.sentEpoch)}
                    </span>
                  </div>
                <div
                  className="notification-content"
                  onClick={() => handleRowClick(n.id, n.notification_route)}
                >
                  
                  
                  <p className="message"><span className="title">{n.notification_title}</span> {n.message}</p>
                  <div className="clear-btn" onClick={() => handleRowClear(n.id)}>
                    <CloseIcon />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
