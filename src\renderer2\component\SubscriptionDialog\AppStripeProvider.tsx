import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { useSubscriptionStore } from '@bryzos/giss-ui-library';

const options = {
  fonts: [
    {
      cssSrc: "https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap",
    },
  ],
};

interface AppStripeProviderProps {
  children: React.ReactNode;
}

const AppStripeProvider: React.FC<AppStripeProviderProps> = ({ children }) => {
  const { subscriptionDialogOpen, isStripeLoaded, setIsStripeLoaded } = useSubscriptionStore();
  const [stripePromise, setStripePromise] = useState<any>(null);

  useEffect(() => {
    // Only load Stripe when the subscription dialog is opened
    if (subscriptionDialogOpen) {
      const loadStripeInstance = async () => {
        try {
          const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY|| '');
          setStripePromise(stripe);
          setIsStripeLoaded(true);
        } catch (error) {
          console.error('Failed to load Stripe:', error);
        }
      };
      loadStripeInstance();
    }
  }, [subscriptionDialogOpen]);


  // If Stripe is not loaded yet, render children without Elements
  if (!stripePromise && !isStripeLoaded) {
    return <>{children}</>;
  }

  return (
    <Elements stripe={stripePromise} options={options}>
      {children}
    </Elements>
  );
};

export default AppStripeProvider; 