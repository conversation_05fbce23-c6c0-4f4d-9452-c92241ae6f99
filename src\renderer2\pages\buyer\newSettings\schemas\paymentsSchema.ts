import * as yup from 'yup';

export const paymentsSchema = yup.object().shape({
  // Placeholder for Payments tab fields

  creditLimit: yup.number().min(0, 'Credit limit must be greater than 0'),
  outstanding: yup.number().min(0, 'Outstanding must be greater than 0'),
  available: yup.number().min(0, 'Available must be greater than 0'),
  accountName: yup.string(),
  bankRoutingNumber: yup.string(),
  bankAccountNumber: yup.string(),

  cardType: yup.string(),
  cardNumberLast4Digits: yup.string(),
  cardExpiry: yup.string(),
  cardEmailId: yup.string(),
  cardFirstName: yup.string().required('Card First Name is required'),
  cardLastName: yup.string().required('Card Last Name is required'),
  billingZipCode: yup.string().min(4, 'Zip is not valid').required('Zip is required'),

  // BNPL
  bnplAvailable: yup.boolean(),
  net30CheckBox: yup.string(),
  einNumber: yup.string().trim().test("isRequired", "Ein Number is not valid", function (value) {
    if (!/^x{5}\d{4}$|^\d{2}-\d{7}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  dnBNumber: yup.string().test("isRequired", "D&B Number is not valid", function (value) {
    if (!/^x{5}\d{4}$|^\d{9}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  creditLine: yup.string().test("isRequired", "Credit Line is not valid", function (value) {
    if (value) {
      return +value > 0;
    } else {
      return false;
    }
  }),
  requestedCreditLimit: yup.string(),
  balanceCreditLimit: yup.string(),
  requestedIncreaseCredit: yup.string(),
  availableBalance: yup.string(),
  outstandingAmount: yup.string(),
  achCheckBox: yup.string(),
  bankName: yup.string(),
  routingNo: yup.string(),
  accountNo: yup.string(),
  requestCreditLine: yup.string(),
  refAccountNo: yup.string(),
  refRoutingNo: yup.string(),
  max_restricted_amount: yup.string(),
});

export type PaymentsFormData = yup.InferType<typeof paymentsSchema>; 