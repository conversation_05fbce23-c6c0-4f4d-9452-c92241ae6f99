import { useGlobalStore, commomKeys } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import useDialogStore from "../component/DialogPopup/DialogStore";

const usePostCancelBnplRequest = () => {
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();
    const { setShowLoader } = useGlobalStore();

    return useMutation(async () => {
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_API_SERVICE}/user/cancel/bnpl-request`);

            if (response.data?.data) {
                if (
                    typeof response.data.data === "object" &&
                    "error_message" in response.data.data
                ) {
                    // Show error dialog for API error responses
                    showCommonDialog(
                        null,
                        commomKeys.errorContent,
                        commomKeys.actionStatus.error,
                        resetDialogStore,
                        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                    );
                    return null; // Don't throw, just return null to prevent further error handling
                } else {
                    return response.data.data;
                }
            } else {
                return null;
            }
        } catch (error: any) {
            // Show error dialog for network/other errors       
            showCommonDialog(
                null,
                commomKeys.errorContent,
                commomKeys.actionStatus.error,
                resetDialogStore,
                [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
            );
            setShowLoader(false);
            throw new Error(error?.message);
        }
    });
};

export default usePostCancelBnplRequest;
