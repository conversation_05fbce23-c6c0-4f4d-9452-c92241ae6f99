import { axios, commomKeys, useBuyerSettingStore, useGlobalStore} from "@bryzos/giss-ui-library";
import useDialogStore from "../component/DialogPopup/DialogStore";

// Define PriceUnit type directly since we're not importing from priceIntegrator anymore
export type PriceUnit = 'cwt' | 'ft' | 'pc' | 'lb';

/**
 * Example function showing how to use the PriceIntegrator through IPC with the main process
 */
export async function getPriceExample(
  productId: any,
  zipcode: string,
  volume: number,
) {
  const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
  try {
    // Call the main process using the electron bridge
    const {userData, productMapping, fetchProductPricesMutateAsync}: any = useGlobalStore.getState();
    const { buyerSetting } = useBuyerSettingStore.getState();
    const defaultZipCode = buyerSetting?.price_search_zip || '63105';
    const _zipcode = zipcode?.length === 5 ? zipcode :  defaultZipCode;
    const productIdList = Array.isArray(productId) ? productId : [productId];

    const payload = {
      "zip_code": _zipcode,
      "product_id": productIdList,
      "order_size": volume || 500
  };
  if(!fetchProductPricesMutateAsync){
    return {
      success: false,
      error: 'Fetch product prices mutate async is not available'
    }
  }
  const result = await fetchProductPricesMutateAsync(payload);
    if (result.error_message) {
      showCommonDialog(null, result.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
      return null;
    }
    let productPricesObj: any = {};  
    if(!Array.isArray(productId)){
      const product = productMapping[productId];
      const spreadRate = !product?.is_safe_product_code ? userData?.data?.disc_discount_rate : 1;
      console.log("spread rate ", spreadRate);
      productPricesObj = result[productId];
      if(userData?.data?.disc_is_discounted){
        productPricesObj.pc = productPricesObj.pc * spreadRate;
        productPricesObj.ft = productPricesObj.ft * spreadRate;
        productPricesObj.cwt = productPricesObj.cwt * spreadRate;
        productPricesObj.lb = productPricesObj.lb * spreadRate;
      }
    }else{
      Object.keys(result).forEach((key) => {
        const product = productMapping[key];
        const spreadRate = !product?.is_safe_product_code ? userData?.data?.disc_discount_rate : 1;
        console.log("spread rate ", spreadRate);
        if(userData?.data?.disc_is_discounted){
          result[key].pc = result[key].pc * spreadRate;
          result[key].ft = result[key].ft * spreadRate;
          result[key].cwt = result[key].cwt * spreadRate;
          result[key].lb = result[key].lb * spreadRate;
        }
      });
      productPricesObj = result;
    }
    console.log("check pricing result data ", result, " and spread  added  pricing result data", productPricesObj);
    if(productPricesObj?.error){
      return null;
    }
    return productPricesObj;
  } catch (error) {
    showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
    return null;
  }
} 