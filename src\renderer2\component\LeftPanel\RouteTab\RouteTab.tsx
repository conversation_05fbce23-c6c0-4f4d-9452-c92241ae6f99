import React from 'react'
import styles from './RouteTab.module.scss';
import { useLocation, useNavigate } from 'react-router-dom';
import { purchaseOrder, routes, sellerView, sellerViewConstants } from 'src/renderer2/common';
import { useAuthStore, useCreatePoStore, useGlobalStore, useSellerOrderStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import { ReactComponent as InstantPriceIcon } from '../../../assets/New-images/New-Image-latest/Instant Price Search.svg';
import { ReactComponent as InstantPriceIconHover } from '../../../assets/New-images/New-Image-latest/Instant Price Search Active.svg';
import { ReactComponent as DeleteIcon } from '../../../assets/New-images/New-Image-latest/routes-delete-icon.svg';
import { useBomPdfExtractorStore } from 'src/renderer2/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';

const RouteTab = () => {
  const navigate = useNavigate();
  const {initiateLogout } = useAuthStore();
  const {userData} = useGlobalStore();
  const purchaseOrdersList = useSellerOrderStore((state: any) => state.ordersCart);
  const {viewedOrdersListForBadgeCount} = useGlobalStore();
  const setBomData = useBomPdfExtractorStore(state => state.setBomData);
  const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
  const location = useLocation();
  
  
  const handleLogout = () => {
    // setCloseWithoutAnimation(true);
    // setOpenLeftPanel(false);
    initiateLogout(false, false, true);
}

  const previewOrders = purchaseOrdersList?.filter(order =>
    order.claimed_by === purchaseOrder.pending
  );

  const previewUnviewedCount = previewOrders?.reduce((count, order) => {
    return viewedOrdersListForBadgeCount?.includes(order.buyer_po_number) ? count : count + 1;
  }, 0);

  const claimOrders = purchaseOrdersList?.filter(order =>
    order.claimed_by === purchaseOrder.readyToClaim
  );
  
  const claimUnviewedCount = claimOrders?.reduce((count, order) => {
    return viewedOrdersListForBadgeCount?.includes(order.buyer_po_number) ? count : count + 1;
  }, 0);

  const handleRouteTabClick = (route: string) => {
    if(location.pathname === route) return;
    setSelectedQuote(null);
    setBomData(null);
    navigate(route);
  }


  return (
    <div className={styles.navTab}>
        <div className={styles.routingPanel}>
          {
            userData?.data?.type === 'BUYER'  ? (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing)} onClick={() => navigate(routes.homePage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      { location.pathname === routes.homePage ? <InstantPriceIconHover /> : <InstantPriceIcon />}
                      {location.pathname !== routes.homePage &&<div className={clsx(styles.routeOptions, styles.pricingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.pricingPosition)}></span>
                        <span className={styles.instantPriceIcon}>
                          <InstantPriceIcon className={styles.instantPriceIcon1} />
                          <InstantPriceIconHover className={styles.instantPriceIcon2} />
                        </span>
                        <span className={clsx(styles.animationEffect)}>INSTANT PRICE SEARCH</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button className={clsx(styles.sideBarButton, styles.quoting, location.pathname === routes.quotePage && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.quotePage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      Q
                      {location.pathname !== routes.quotePage && <div className={clsx(styles.routeOptions, styles.quotingHover)}>
                        <span className={clsx(styles.optionHoverBg2)}></span>
                        <span>Q</span>
                        <span className={clsx(styles.animationEffect)}>UOTING</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button className={clsx(styles.sideBarButton, styles.purchasing, location.pathname === routes.createPoPage && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.createPoPage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      P
                      {location.pathname !== routes.createPoPage && <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>P</span>
                        <span className={clsx(styles.animationEffect)}>URCHASING</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button className={clsx(styles.sideBarButton, styles.order, location.pathname === routes.orderManagementPage && styles.sideBarButtonActive)} onClick={() => handleRouteTabClick(routes.orderManagementPage)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      O 
                      {location.pathname !== routes.orderManagementPage && <div className={clsx(styles.routeOptions, styles.orderHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.orderPosition)}></span>
                        <span>O</span>
                        <span className={clsx(styles.animationEffect)}>RDER MANAGEMENT</span>
                      </div>}
                    </span> 
                  </div>
                </button>
                <button className={clsx(styles.sideBarButton, styles.deletedItems)}>
                  <div className={styles.mainButton}>
                    <span className={styles.deleteIcon}>
                      <DeleteIcon />
                      {/* <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>D</span>
                        <span className={clsx(styles.animationEffect)}>ELETE</span>
                      </div> */}
                    </span>
                  </div>
                </button>
              </>
            ) : (
              <>
                <button className={clsx(styles.sideBarButton, styles.pricing, location.pathname === routes.previewOrderPage && styles.sideBarButtonActive)} onClick={() => {navigate(routes.previewOrderPage);}}>
                <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      P
                      {location.pathname !== routes.previewOrderPage && <div className={clsx(styles.routeOptions, styles.previewHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.quotingPosition)}></span>
                        <span>P</span>
                        <span className={clsx(styles.animationEffect)}>REVIEW ORDERS</span>
                      </div>}
                    </span>
                  </div>
                  {previewUnviewedCount > 0 && <span className={styles.badge}>{previewUnviewedCount}</span>}
                </button>
                
                <button className={clsx(styles.sideBarButton, styles.quoting, location.pathname === routes.orderPage && styles.sideBarButtonActive)} onClick={() => {navigate(routes.orderPage);}}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      C
                      {location.pathname !== routes.orderPage && <div className={clsx(styles.routeOptions, styles.claimHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.quotingPosition)}></span>
                        <span>C</span>
                        <span className={clsx(styles.animationEffect)}>LAIM ORDERS</span>
                      </div>}
                    </span>
                  </div>
                  {claimUnviewedCount > 0 && <span className={styles.badge}>{claimUnviewedCount}</span>}
                </button>
                
                <button className={clsx(styles.sideBarButton, styles.order, location.pathname === routes.orderManagementPage && styles.sideBarButtonActive)}>
                  <div className={styles.mainButton}>
                    <span className={styles.positionRelative}>
                      O
                      {location.pathname !== routes.orderManagementPage && <div className={clsx(styles.routeOptions, styles.orderHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.orderPosition)}></span>
                        <span>O</span>
                        <span className={clsx(styles.animationEffect)}>RDER MANAGEMENT</span>
                      </div>}
                    </span>
                  </div>
                </button>
                <button className={clsx(styles.sideBarButton, styles.deletedItems, location.pathname === routes.deleteOrderPage && styles.sideBarButtonActive)} onClick={() => {navigate(routes.deleteOrderPage)}}>
                  <div className={styles.mainButton}>
                    <span className={styles.deleteIcon}>
                      <DeleteIcon />
                      {/* <div className={clsx(styles.routeOptions, styles.purchasingHover)}>
                        <span className={clsx(styles.optionHoverBg2, styles.purchasingPosition)}></span>
                        <span>D</span>
                        <span className={clsx(styles.animationEffect)}>ELETE</span>
                      </div> */}
                    </span>
                  </div>
                </button>
              </>
            )
          }
        </div>
        <div className={styles.logout}>
            <button className={styles.logoutButton} onClick={handleLogout}>Logout</button>
        </div>  
    </div>
  )
}

export default RouteTab