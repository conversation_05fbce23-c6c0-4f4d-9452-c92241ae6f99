// @ts-nocheck
import MenuItem from '@mui/material/MenuItem';
import { ListSubheader, Select } from '@mui/material';
import { Controller } from 'react-hook-form';
import { useImmer } from 'use-immer';
import { useEffect } from 'react';

export function CustomMenu({ items, className, MenuProps, control, name, onChange, defaultValue, placeholder, renderValue, IconComponent,category, onOpen, onClose, keyHandler, onfocus, onBlur, onClick, disabled, onKeyDown, tabIndex, disableAutoFocusItem = false , onFocusKeyDown}) {
    const [openLeftPanel, setOpenLeftPanel] = useImmer(false);
    const [onFocus, setonFocus] = useImmer(false);

    const keyupHanlder = (event) => {
        if (event.key === "Tab" || event.key === "Escape") {
            if (onFocus) {
                onFocusKeyDown?.(event)  
                if (openLeftPanel) {
                    setOpenLeftPanel(false);
                } else if (items?.length) {
                    setOpenLeftPanel(true);
                    if(keyHandler) keyHandler();
                }
            }
        }
    }

    const keydownHanlder = (event) => {
        onKeyDown?.(event);
        if(onFocus){
            onFocusKeyDown?.(event)
        }
    }

    useEffect(() => {
        window.addEventListener('keydown', keydownHanlder);
        window.addEventListener('keyup', keyupHanlder);

        return () => {
            window.removeEventListener('keydown', keydownHanlder);
            window.removeEventListener('keyup', keyupHanlder);
        }
    })

    return (
        <>
            <Controller
                control={control}
                name={name}
                defaultValue={defaultValue}
                render={({ field: { value, ...rest } }) => {
                    return (
                        <Select MenuProps={{
                            ...MenuProps,
                            disableAutoFocusItem: disableAutoFocusItem
                        }}
                            value={value ?? ''}
                            // {...rest}
                            displayEmpty
                            renderValue={renderValue ? renderValue : value ? undefined : () => <span className='dropdownPlaceholderText'>{placeholder}</span> ?? ''}
                            onChange={(e) => {
                                rest.onChange(e)
                                onChange?.(e)
                                setOpenLeftPanel(false);
                            }}
                            className={className}
                            classes={{
                                select: 'selectDropdown'
                            }}
                            IconComponent={IconComponent}
                            open={disabled ? false : openLeftPanel}
                            onClick={(e) => {
                                if(e.target.innerText === category || e.target.localName === "ul" ||  Object.keys(e.target.dataset).length === 0 && e.target.id.length !== 0){
                                    setOpenLeftPanel(true);
                                    onClick?.()
                                    return;
                                }
                                setOpenLeftPanel(!openLeftPanel);
                                setonFocus(true);
                            }
                            }
                            onFocus={() => {
                                setonFocus(true)
                                onfocus?.()
                            }}
                            onBlur={() => {
                                setonFocus(false);
                                setOpenLeftPanel(false);
                                onBlur?.()
                            }}
                            onOpen={onOpen}
                            onClose={onClose}
                            tabIndex={tabIndex}
                        >
                             {category && <ListSubheader>{category}</ListSubheader>}
                            {items.map((item) => (
                                <MenuItem key={item.value} value={item.value} disabled={item.disabled}>
                                    {item.available ?
                                        item.changeTitle :
                                        item.title
                                    }
                                </MenuItem>
                            ))}
                        </Select>
                    )
                }}
            />

        </>
    );
}
