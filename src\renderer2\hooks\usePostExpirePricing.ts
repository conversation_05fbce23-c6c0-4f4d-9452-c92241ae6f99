import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const usePostExpirePricing = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload) => {
      try {
        const url = `${import.meta.env.VITE_API_SERVICE}/user/expire/pricing`;
        const response = await axios.post(url, payload);
        return response;
      } catch (error) {
        throw error;
      }
    }
  });
};

export default usePostExpirePricing; 