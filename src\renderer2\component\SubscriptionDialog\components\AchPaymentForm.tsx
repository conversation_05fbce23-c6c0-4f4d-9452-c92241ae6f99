import React from 'react';
import { useFormContext } from 'react-hook-form';
import styles from '../SubscriptionDialog.module.scss';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';
import { Fade } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../../assets/New-images/StateIconDropDpown.svg';
import { validateAccountNumber, validateRoutingNumber } from 'src/renderer2/helper';

const AchPaymentForm: React.FC = () => {
  const { register, formState: { errors }, control, watch, setValue, setError, clearErrors } = useFormContext();

  const accountType = [
    { title: 'Checking', value: 'checking' },
    { title: 'Savings', value: 'savings' }
  ];



  // Handle input change for bank account fields with real-time validation
  const handleBankAccountInputChange = (field: string, value: string): void => {
    setValue(field as any, value , { shouldDirty: true });
    clearErrors(field as any);

    // Check if any of the three fields contain 'x' and empty all if so
    const routingNumber = watch('routingNumber');
    const accountNumber = watch('accountNumber');
    const reEnterAccountNumber = watch('reEnterAccountNumber');
    
    if(value.includes('x') || routingNumber?.includes('x') || accountNumber?.includes('x') || reEnterAccountNumber?.includes('x')){
      setValue('routingNumber', '');
      setValue('accountNumber', '');
      setValue('reEnterAccountNumber', '');
    }

    // Perform real-time validation
    // if (field === 'routingNumber') {
    //   if (value && !validateRoutingNumber(value)) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: value.length !== 9
    //         ? 'Routing number must be exactly 9 digits'
    //         : 'Invalid routing number format'
    //     });
    //   } else {
    //     clearErrors(field as any);
    //   }
    // } else if (field === 'accountNumber') {
    //   if (value && !validateAccountNumber(value)) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: !/^\d+$/.test(value)
    //         ? 'Account number must contain only digits'
    //         : value.length < 8
    //           ? 'Account number must be at least 8 digits'
    //           : 'Account number must not exceed 17 digits'
    //     });
    //   } else {
    //     clearErrors(field as any);

    //     // Also validate reEnterAccountNumber if it exists
    //     const reEnterValue = watch('reEnterAccountNumber');
    //     if (reEnterValue && reEnterValue !== value) {
    //       setError('reEnterAccountNumber', {
    //         type: 'manual',
    //         message: 'Account numbers must match'
    //       });
    //     } else if (reEnterValue) {
    //       clearErrors('reEnterAccountNumber');
    //     }
    //   }
    // } else if (field === 'reEnterAccountNumber') {
    //   const accountNumber = watch('accountNumber');
    //   if (value && accountNumber && value !== accountNumber) {
    //     setError(field as any, {
    //       type: 'manual',
    //       message: 'Account numbers must match'
    //     });
    //   } else {
    //     clearErrors(field as any);
    //   }
    // }
  };

  return (
    <div className={styles.paymentDetailsContainer}>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Name of Account"
            className={styles.formField}
            register={register('accountName')}
            errorInput={errors.accountName?.message}
            aria-label="Name of Account"
          />
        </InputWrapper>
        <div className={styles.accountTypeDropdown}>
            <CustomMenu
            displayEmpty
              control={control}
              name="accountType"
              items={accountType}
              className={styles.paymentMethodSelect}
               renderValue={(selected) => {
                if (!selected) {
                  return <span style={{ color: '#71737f', textTransform:'uppercase', fontFamily:'Syncopate', fontSize:'14px'}}>Account Type</span>; 
                }
                return <span style={{ textTransform:'capitalize'}}>{selected}</span>;
              }}
              MenuProps={{
                classes: {
                  paper: styles.Dropdownpaper,
                  list: styles.muiMenuList,
                }

              }}
              IconComponent={DropdownIcon}
            />
          </div>
      </div>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="Routing Number (ABA)"
            className={styles.formField}
            register={{
              ...register('routingNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('routingNumber', e.target.value)
            }}
            errorInput={errors.routingNumber?.message}
            aria-label="Routing Number (ABA)"
            maxLength={9}
            mode="maskedWholeNumber"
          />
        </InputWrapper>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="BANK NAME"
            className={styles.formField}
            register={register('bankName')}
            errorInput={errors.bankName?.message}
            aria-label="BANK NAME"
          />
        </InputWrapper>
      </div>
      <div className={styles.paymentDetails}>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder={"Account Number"}
            className={styles.formField}
            register={{
              ...register('accountNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('accountNumber', e.target.value)
            }}
            errorInput={errors.accountNumber?.message}
            aria-label="Account Number"
            maxLength={17}
            mode="maskedWholeNumber"
          />
        </InputWrapper>
        <InputWrapper>
          <CustomTextField
            type="text"
            placeholder="RE-ENTER ACCT#"
            className={styles.formField}
            register={{
              ...register('reEnterAccountNumber'),
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('reEnterAccountNumber', e.target.value)
            }}
            errorInput={errors.reEnterAccountNumber?.message}
            aria-label="RE-ENTER ACCT#"
            maxLength={17}
            mode="maskedWholeNumber"
          />
        </InputWrapper>
      </div>
    </div>
  );
};

export default AchPaymentForm; 